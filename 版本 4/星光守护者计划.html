<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务报告："星光守护者"计划</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Noto+Sans+SC:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: #000;
            color: #fff;
            overflow: hidden;
            position: relative;
        }
        
        /* 宇宙背景 */
        .space-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: 
                radial-gradient(ellipse at 20% 20%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(ellipse at 80% 40%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(ellipse at 40% 80%, rgba(120, 219, 226, 0.3) 0%, transparent 50%),
                linear-gradient(135deg, #000011 0%, #001122 25%, #000033 50%, #001144 75%, #000022 100%);
            z-index: -2;
        }
        
        /* 星星背景 */
        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: -1;
            background-image: 
                radial-gradient(2px 2px at 20px 30px, #eee, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: twinkle 4s ease-in-out infinite alternate;
        }
        
        @keyframes twinkle {
            0% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        /* 页面容器 */
        .page {
            position: absolute;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .page.active {
            opacity: 1;
            transform: translateX(0);
        }
        
        .page.prev {
            transform: translateX(-100%);
        }
        
        /* 导航 */
        .navigation {
            position: fixed;
            top: 50%;
            right: 30px;
            transform: translateY(-50%);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .nav-dot.active {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            border-color: #FFD700;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
        }
        
        .nav-dot:hover {
            transform: scale(1.2);
            border-color: rgba(255, 255, 255, 0.8);
        }
        
        /* 页面切换按钮 */
        .page-controls {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 1000;
        }
        
        .control-btn {
            padding: 12px 25px;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2));
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #fff;
            cursor: pointer;
            border-radius: 25px;
            font-family: 'Orbitron', monospace;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .control-btn:hover {
            background: linear-gradient(45deg, rgba(255, 215, 0, 0.3), rgba(255, 165, 0, 0.3));
            border-color: #FFD700;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            transform: translateY(-2px);
        }
        
        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        /* 通用样式 */
        .page-title {
            font-family: 'Orbitron', monospace;
            font-weight: 900;
            font-size: clamp(2rem, 5vw, 4rem);
            text-align: center;
            background: linear-gradient(45deg, #FFD700, #FFA500, #FFD700);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: shimmer 3s ease-in-out infinite;
            margin-bottom: 2rem;
        }
        
        .page-subtitle {
            font-size: clamp(1rem, 3vw, 1.8rem);
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 3rem;
        }
        
        @keyframes shimmer {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        /* 第一页：封面 */
        .page1 .content {
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .spaceship {
            width: 300px;
            height: 150px;
            margin: 3rem auto;
            position: relative;
            animation: float 6s ease-in-out infinite;
        }
        
        .spaceship::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #C0C0C0, #E5E5E5, #C0C0C0);
            clip-path: polygon(0 50%, 15% 0%, 85% 0%, 100% 50%, 85% 100%, 15% 100%);
            box-shadow: 
                0 0 30px rgba(192, 192, 192, 0.5),
                inset 0 0 20px rgba(255, 255, 255, 0.3);
        }
        
        .spaceship::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 20px;
            background: linear-gradient(90deg, transparent, #00BFFF, #87CEEB, #00BFFF, transparent);
            border-radius: 50px;
            filter: blur(8px);
            animation: thruster 2s ease-in-out infinite alternate;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-20px); }
        }
        
        @keyframes thruster {
            0% { opacity: 0.7; transform: translateX(-50%) scaleX(0.8); }
            100% { opacity: 1; transform: translateX(-50%) scaleX(1.2); }
        }
        
        .planet {
            position: absolute;
            top: 60%;
            right: 15%;
            width: 120px;
            height: 120px;
            background: 
                radial-gradient(circle at 30% 30%, #87CEEB, #4169E1, #191970);
            border-radius: 50%;
            box-shadow: 
                0 0 40px rgba(65, 105, 225, 0.6),
                inset -10px -10px 30px rgba(0, 0, 0, 0.3);
            animation: rotate 20s linear infinite;
        }
        
        .planet::after {
            content: '';
            position: absolute;
            top: 20%;
            left: 25%;
            width: 40%;
            height: 30%;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            filter: blur(2px);
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 卡片通用样式 */
        .card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(255, 215, 0, 0.3);
            border-color: rgba(255, 215, 0, 0.5);
        }
        
        /* 动画关键帧 */
        @keyframes danger-flash {
            0%, 100% { left: -100%; }
            50% { left: 100%; }
        }
        
        @keyframes hologram-scan {
            0% { transform: translateY(0); }
            100% { transform: translateY(100%); }
        }
        
        @keyframes signal-pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }
        
        @keyframes radar-sweep {
            0% { transform: translate(-1px, -180px) rotate(0deg); }
            100% { transform: translate(-1px, -180px) rotate(360deg); }
        }
        
        @keyframes nebula-pulse {
            0%, 100% { transform: scale(1); box-shadow: 0 0 20px rgba(255,215,0,0.5); }
            50% { transform: scale(1.1); box-shadow: 0 0 40px rgba(255,215,0,0.8); }
        }
        
        @keyframes medal-shine {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.05) rotate(5deg); }
        }
        
        @keyframes medal-glow {
            0%, 100% { box-shadow: 0 0 20px rgba(255,215,0,0.5); }
            50% { box-shadow: 0 0 40px rgba(255,215,0,1); }
        }
        
        @keyframes station-light {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }
        
        @keyframes return-flight {
            0% { transform: translateY(-50%) translateX(0); }
            50% { transform: translateY(-60%) translateX(300px); }
            100% { transform: translateY(-50%) translateX(600px); }
        }
        
        @keyframes light-beam-appear {
            0% { opacity: 0; height: 0; }
            100% { opacity: 1; height: 200px; }
        }
        
        .blink {
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .navigation {
                right: 15px;
            }
            
            .nav-dot {
                width: 10px;
                height: 10px;
            }
            
            .page-controls {
                bottom: 15px;
            }
            
            .control-btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }
            
            .spaceship {
                width: 200px;
                height: 100px;
            }
            
            .planet {
                width: 80px;
                height: 80px;
            }
            
            .bridge-screen {
                width: 90% !important;
                height: 300px !important;
            }
            
            .crew-names {
                gap: 1rem !important;
            }
            
            .crew-name {
                font-size: 1rem !important;
                padding: 0.8rem 1.5rem !important;
            }
            
            .return-journey {
                width: 100% !important;
                height: 150px !important;
            }
        }
        
    </style>
</head>
<body>
    <div class="space-bg"></div>
    <div class="stars"></div>
    
    <!-- 导航点 -->
    <div class="navigation">
        <div class="nav-dot active" data-page="1"></div>
        <div class="nav-dot" data-page="2"></div>
        <div class="nav-dot" data-page="3"></div>
        <div class="nav-dot" data-page="4"></div>
        <div class="nav-dot" data-page="5"></div>
        <div class="nav-dot" data-page="6"></div>
        <div class="nav-dot" data-page="7"></div>
        <div class="nav-dot" data-page="8"></div>
        <div class="nav-dot" data-page="9"></div>
    </div>
    
    <!-- 页面控制按钮 -->
    <div class="page-controls">
        <button class="control-btn" id="prevBtn" disabled>← 上一页</button>
        <button class="control-btn" id="nextBtn">下一页 →</button>
    </div>
    
    <!-- 第1页：封面 -->
    <div class="page active" id="page1">
        <div class="content">
            <h1 class="page-title">任务报告："星光守护者"计划</h1>
            <div class="page-subtitle">福建大学生安全知识竞赛 - 航行复盘</div>
            <div class="spaceship"></div>
            <div class="planet"></div>
            <div style="margin-top: 2rem; font-size: 1.2rem; color: rgba(255,255,255,0.7);">
                守护者号正从超空间航行中出现，目标前方的蓝色星球...
            </div>
        </div>
    </div>
    
    <!-- 第2页：舰桥船员 -->
    <div class="page" id="page2">
        <div class="content" style="width: 90%; max-width: 1200px;">
            <h2 class="page-title">"守护者号"核心船员</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">
                
                <div class="card crew-card" data-member="赵明">
                    <div style="text-align: center;">
                        <div style="width: 80px; height: 80px; margin: 0 auto 1rem; background: linear-gradient(45deg, #FFD700, #FFA500); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; font-weight: bold;">舰</div>
                        <h3 style="font-family: 'Orbitron', monospace; font-size: 1.5rem; margin-bottom: 0.5rem; color: #FFD700;">赵明</h3>
                        <div style="color: #C0C0C0; font-size: 1.1rem; margin-bottom: 1rem;">舰长 (主持)</div>
                        <div class="member-description" style="opacity: 0; height: 0; transition: all 0.3s ease; color: rgba(255,255,255,0.8);">
                            指挥整个守护者号的航行任务，负责重大决策和团队协调
                        </div>
                    </div>
                </div>
                
                <div class="card crew-card" data-member="马佳平">
                    <div style="text-align: center;">
                        <div style="width: 80px; height: 80px; margin: 0 auto 1rem; background: linear-gradient(45deg, #C0C0C0, #E5E5E5); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; font-weight: bold; color: #333;">副</div>
                        <h3 style="font-family: 'Orbitron', monospace; font-size: 1.5rem; margin-bottom: 0.5rem; color: #FFD700;">马佳平</h3>
                        <div style="color: #C0C0C0; font-size: 1.1rem; margin-bottom: 1rem;">大副 (后台导演)</div>
                        <div class="member-description" style="opacity: 0; height: 0; transition: all 0.3s ease; color: rgba(255,255,255,0.8);">
                            协助舰长管理日常事务，统筹后台操作和流程控制
                        </div>
                    </div>
                </div>
                
                <div class="card crew-card" data-member="蔡庆强">
                    <div style="text-align: center;">
                        <div style="width: 80px; height: 80px; margin: 0 auto 1rem; background: linear-gradient(45deg, #00CED1, #20B2AA); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; font-weight: bold;">工</div>
                        <h3 style="font-family: 'Orbitron', monospace; font-size: 1.5rem; margin-bottom: 0.5rem; color: #FFD700;">蔡庆强</h3>
                        <div style="color: #C0C0C0; font-size: 1.1rem; margin-bottom: 1rem;">总工程师 (技术)</div>
                        <div class="member-description" style="opacity: 0; height: 0; transition: all 0.3s ease; color: rgba(255,255,255,0.8);">
                            负责所有技术系统的运行和维护，确保飞船各项设备正常工作
                        </div>
                    </div>
                </div>
                
                <div class="card crew-card" data-member="陈柄宏">
                    <div style="text-align: center;">
                        <div style="width: 80px; height: 80px; margin: 0 auto 1rem; background: linear-gradient(45deg, #9370DB, #8A2BE2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; font-weight: bold;">航</div>
                        <h3 style="font-family: 'Orbitron', monospace; font-size: 1.5rem; margin-bottom: 0.5rem; color: #FFD700;">陈柄宏</h3>
                        <div style="color: #C0C0C0; font-size: 1.1rem; margin-bottom: 1rem;">领航员 (VJ)</div>
                        <div class="member-description" style="opacity: 0; height: 0; transition: all 0.3s ease; color: rgba(255,255,255,0.8);">
                            制定航行路线，负责视觉呈现和导航系统操作
                        </div>
                    </div>
                </div>
                
                <div class="card crew-card" data-member="梁金隆">
                    <div style="text-align: center;">
                        <div style="width: 80px; height: 80px; margin: 0 auto 1rem; background: linear-gradient(45deg, #FF6347, #FF4500); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; font-weight: bold;">通</div>
                        <h3 style="font-family: 'Orbitron', monospace; font-size: 1.5rem; margin-bottom: 0.5rem; color: #FFD700;">梁金隆</h3>
                        <div style="color: #C0C0C0; font-size: 1.1rem; margin-bottom: 1rem;">通讯官 (DJ)</div>
                        <div class="member-description" style="opacity: 0; height: 0; transition: all 0.3s ease; color: rgba(255,255,255,0.8);">
                            负责所有通讯联络和音频系统管理
                        </div>
                    </div>
                </div>
                
                <div class="card crew-card" data-member="盛捷">
                    <div style="text-align: center;">
                        <div style="width: 80px; height: 80px; margin: 0 auto 1rem; background: linear-gradient(45deg, #32CD32, #228B22); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; font-weight: bold;">科</div>
                        <h3 style="font-family: 'Orbitron', monospace; font-size: 1.5rem; margin-bottom: 0.5rem; color: #FFD700;">盛捷</h3>
                        <div style="color: #C0C0C0; font-size: 1.1rem; margin-bottom: 1rem;">科学官 (计分)</div>
                        <div class="member-description" style="opacity: 0; height: 0; transition: all 0.3s ease; color: rgba(255,255,255,0.8);">
                            负责数据分析和科学计算，管理任务评分系统
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 第3页：航行日志 -->
    <div class="page" id="page3">
        <div class="content" style="width: 90%; max-width: 1000px; text-align: center;">
            <h2 class="page-title">航行日志：闪亮的星尘记忆</h2>
            <div style="position: relative; margin: 3rem auto; width: 600px; height: 400px;">
                <!-- 星图背景 -->
                <svg width="600" height="400" style="position: absolute; top: 0; left: 0;">
                    <defs>
                        <radialGradient id="starCluster" cx="50%" cy="50%" r="30%">
                            <stop offset="0%" style="stop-color:#FFD700;stop-opacity:0.8" />
                            <stop offset="50%" style="stop-color:#FFA500;stop-opacity:0.4" />
                            <stop offset="100%" style="stop-color:#FF6347;stop-opacity:0.1" />
                        </radialGradient>
                    </defs>
                    
                    <!-- 星团1 -->
                    <circle class="star-cluster" cx="150" cy="120" r="40" fill="url(#starCluster)" 
                            style="cursor: pointer; transition: all 0.3s ease;" data-memory="1">
                        <animate attributeName="r" values="35;45;35" dur="3s" repeatCount="indefinite"/>
                    </circle>
                    <text x="150" y="125" text-anchor="middle" fill="#fff" font-family="Orbitron" font-size="12" style="pointer-events: none;">星团α</text>
                    
                    <!-- 星团2 -->
                    <circle class="star-cluster" cx="350" cy="200" r="40" fill="url(#starCluster)" 
                            style="cursor: pointer; transition: all 0.3s ease;" data-memory="2">
                        <animate attributeName="r" values="35;45;35" dur="3s" repeatCount="indefinite" begin="1s"/>
                    </circle>
                    <text x="350" y="205" text-anchor="middle" fill="#fff" font-family="Orbitron" font-size="12" style="pointer-events: none;">星团β</text>
                    
                    <!-- 星团3 -->
                    <circle class="star-cluster" cx="480" cy="100" r="40" fill="url(#starCluster)" 
                            style="cursor: pointer; transition: all 0.3s ease;" data-memory="3">
                        <animate attributeName="r" values="35;45;35" dur="3s" repeatCount="indefinite" begin="2s"/>
                    </circle>
                    <text x="480" y="105" text-anchor="middle" fill="#fff" font-family="Orbitron" font-size="12" style="pointer-events: none;">星团γ</text>
                    
                    <!-- 连接线 -->
                    <path d="M 150 120 Q 250 160 350 200" stroke="rgba(255,215,0,0.3)" stroke-width="2" fill="none">
                        <animate attributeName="stroke-dasharray" values="0,1000;1000,0" dur="4s" repeatCount="indefinite"/>
                    </path>
                    <path d="M 350 200 Q 420 150 480 100" stroke="rgba(255,215,0,0.3)" stroke-width="2" fill="none">
                        <animate attributeName="stroke-dasharray" values="0,1000;1000,0" dur="4s" repeatCount="indefinite" begin="1s"/>
                    </path>
                </svg>
                
                <!-- 记忆显示区域 -->
                <div id="memoryDisplay" style="position: absolute; bottom: -80px; left: 0; right: 0; opacity: 0; transition: all 0.5s ease;">
                    <div class="card" style="padding: 1.5rem; background: rgba(0,0,0,0.8);">
                        <p id="memoryText" style="font-size: 1.1rem; line-height: 1.6; color: #FFD700;"></p>
                    </div>
                </div>
            </div>
            
            <div style="margin-top: 8rem; color: rgba(255,255,255,0.7); font-size: 1rem;">
                点击星团查看那些闪亮的记忆时刻...
            </div>
        </div>
    </div>
    
    <!-- 第4页：穿越小行星带 -->
    <div class="page" id="page4">
        <div class="content" style="width: 90%; max-width: 1200px;">
            <h2 class="page-title">警报！穿越小行星带！</h2>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 3rem; gap: 3rem;">
                
                <!-- 左侧：小行星挑战 -->
                <div style="flex: 1;">
                    <h3 style="color: #FF6347; font-family: 'Orbitron'; margin-bottom: 2rem; font-size: 1.5rem;">
                        ⚠️ 检测到威胁 ⚠️
                    </h3>
                    
                    <div class="asteroid-zone">
                        <div class="asteroid" data-challenge="1" style="margin-bottom: 1.5rem;">
                            <div class="asteroid-body">
                                <div style="background: linear-gradient(45deg, #8B0000, #DC143C); padding: 1rem 1.5rem; border-radius: 10px; cursor: pointer; border: 2px solid #FF6347; position: relative; overflow: hidden;">
                                    <div style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); animation: danger-flash 2s infinite;"></div>
                                    <div style="font-weight: bold; color: #FFD700;">陨石 #001</div>
                                    <div style="color: #fff; margin-top: 0.5rem;">计分表记录难题</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="asteroid" data-challenge="2" style="margin-bottom: 1.5rem;">
                            <div class="asteroid-body">
                                <div style="background: linear-gradient(45deg, #8B0000, #DC143C); padding: 1rem 1.5rem; border-radius: 10px; cursor: pointer; border: 2px solid #FF6347; position: relative; overflow: hidden;">
                                    <div style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); animation: danger-flash 2s infinite 0.7s;"></div>
                                    <div style="font-weight: bold; color: #FFD700;">陨石 #002</div>
                                    <div style="color: #fff; margin-top: 0.5rem;">寻找合适的氛围音乐</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="asteroid" data-challenge="3" style="margin-bottom: 1.5rem;">
                            <div class="asteroid-body">
                                <div style="background: linear-gradient(45deg, #8B0000, #DC143C); padding: 1rem 1.5rem; border-radius: 10px; cursor: pointer; border: 2px solid #FF6347; position: relative; overflow: hidden;">
                                    <div style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); animation: danger-flash 2s infinite 1.4s;"></div>
                                    <div style="font-weight: bold; color: #FFD700;">陨石 #003</div>
                                    <div style="color: #fff; margin-top: 0.5rem;">计时器接口接触不良</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：解决方案显示 -->
                <div style="flex: 1;">
                    <div id="solutionDisplay" style="opacity: 0; transition: all 0.5s ease;">
                        <div style="text-align: center; margin-bottom: 2rem;">
                            <div class="spaceship-evasion" style="width: 200px; height: 100px; margin: 0 auto; position: relative;">
                                <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #C0C0C0, #E5E5E5); clip-path: polygon(0 50%, 15% 0%, 85% 0%, 100% 50%, 85% 100%, 15% 100%); transform: translateX(0); transition: transform 0.8s ease;"></div>
                            </div>
                            <div style="color: #00FF00; font-family: 'Orbitron'; margin-top: 1rem; font-size: 1.2rem;">
                                规避成功！
                            </div>
                        </div>
                        
                        <div class="card" style="background: rgba(0,100,0,0.2); border-color: #00FF00;">
                            <h4 style="color: #00FF00; font-family: 'Orbitron'; margin-bottom: 1rem;">解决方案：</h4>
                            <p id="solutionText" style="color: #fff; line-height: 1.6; font-size: 1.1rem;"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 第5页：来自过去的通讯 -->
    <div class="page" id="page5">
        <div class="content" style="width: 90%; max-width: 800px; text-align: center;">
            <h2 class="page-title">捕获来自过去的通讯</h2>
            
            <div style="margin: 3rem auto; position: relative;">
                <!-- 全息投影效果 -->
                <div class="hologram-container" style="width: 400px; height: 300px; margin: 0 auto; position: relative; border: 2px solid rgba(0,255,255,0.5); border-radius: 20px; overflow: hidden;">
                    <div class="hologram-lines" style="position: absolute; width: 100%; height: 100%; background: repeating-linear-gradient(0deg, transparent, transparent 4px, rgba(0,255,255,0.1) 4px, rgba(0,255,255,0.1) 8px); animation: hologram-scan 2s linear infinite;"></div>
                    
                    <div id="messageDisplay" style="padding: 2rem; height: 100%; display: flex; flex-direction: column; justify-content: center; position: relative; z-index: 2;">
                        <div class="message-indicator" style="color: #00FFFF; font-family: 'Orbitron'; margin-bottom: 1rem; font-size: 0.9rem;">
                            >>> 接收中... <span class="blink">█</span>
                        </div>
                        <div id="currentMessage" style="color: #FFD700; font-size: 1.3rem; line-height: 1.6; min-height: 120px; display: flex; align-items: center; justify-content: center;">
                            点击开始接收通讯...
                        </div>
                    </div>
                    
                    <!-- 控制按钮 -->
                    <div style="position: absolute; bottom: 10px; right: 10px;">
                        <button id="nextMessage" class="control-btn" style="padding: 8px 15px; font-size: 0.8rem; background: rgba(0,255,255,0.2); border-color: #00FFFF;">
                            下一条 →
                        </button>
                    </div>
                </div>
                
                <!-- 信号强度指示器 -->
                <div style="margin-top: 2rem; display: flex; justify-content: center; gap: 10px;">
                    <div class="signal-bar" style="width: 4px; height: 20px; background: #00FF00; animation: signal-pulse 1s infinite;"></div>
                    <div class="signal-bar" style="width: 4px; height: 25px; background: #00FF00; animation: signal-pulse 1s infinite 0.2s;"></div>
                    <div class="signal-bar" style="width: 4px; height: 30px; background: #00FF00; animation: signal-pulse 1s infinite 0.4s;"></div>
                    <div class="signal-bar" style="width: 4px; height: 25px; background: #00FF00; animation: signal-pulse 1s infinite 0.6s;"></div>
                    <div class="signal-bar" style="width: 4px; height: 20px; background: #00FF00; animation: signal-pulse 1s infinite 0.8s;"></div>
                </div>
                
                <div style="color: rgba(255,255,255,0.7); margin-top: 1rem;">
                    信号强度: 强 | 来源: 时空坐标 [过去]
                </div>
            </div>
        </div>
    </div>
    
    <!-- 第6页：啊哈！时刻 -->
    <div class="page" id="page6">
        <div class="content" style="width: 90%; max-width: 1000px; text-align: center;">
            <h2 class="page-title">啊哈！发现"效率"星云！</h2>
            
            <div style="margin: 3rem auto; position: relative; width: 600px; height: 400px;">
                <!-- 雷达扫描界面 -->
                <div class="radar-screen" style="width: 400px; height: 400px; margin: 0 auto; border: 3px solid #00FF00; border-radius: 50%; position: relative; background: radial-gradient(circle, rgba(0,255,0,0.1) 0%, rgba(0,255,0,0.05) 40%, transparent 70%);">
                    
                    <!-- 雷达扫描线 -->
                    <div class="radar-sweep" style="position: absolute; top: 50%; left: 50%; width: 2px; height: 180px; background: linear-gradient(to bottom, #00FF00, transparent); transform-origin: 0 0; transform: translate(-1px, -180px) rotate(0deg); animation: radar-sweep 4s linear infinite;"></div>
                    
                    <!-- 同心圆 -->
                    <div style="position: absolute; top: 25%; left: 25%; width: 50%; height: 50%; border: 1px solid rgba(0,255,0,0.3); border-radius: 50%;"></div>
                    <div style="position: absolute; top: 12.5%; left: 12.5%; width: 75%; height: 75%; border: 1px solid rgba(0,255,0,0.3); border-radius: 50%;"></div>
                    
                    <!-- 星云目标 -->
                    <div id="nebulaTarget" class="nebula-target" style="position: absolute; top: 30%; right: 25%; width: 60px; height: 60px; background: radial-gradient(circle, #FFD700, #FFA500, #FF6347); border-radius: 50%; opacity: 0; cursor: pointer; transition: all 0.5s ease; animation: nebula-pulse 2s infinite;">
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #fff; font-size: 0.8rem; font-weight: bold;">效率</div>
                    </div>
                    
                    <!-- 坐标显示 -->
                    <div style="position: absolute; bottom: -40px; left: 50%; transform: translateX(-50%); color: #00FF00; font-family: 'Orbitron'; font-size: 0.9rem;">
                        扫描中... <span id="scanProgress">0%</span>
                    </div>
                </div>
                
                <!-- 发现结果 -->
                <div id="discoveryResult" style="position: absolute; right: -200px; top: 50%; transform: translateY(-50%); width: 300px; opacity: 0; transition: all 0.8s ease;">
                    <div class="card" style="background: rgba(255,215,0,0.1); border-color: #FFD700;">
                        <h3 style="color: #FFD700; font-family: 'Orbitron'; margin-bottom: 1rem; font-size: 1.3rem;">
                            🔍 新发现！
                        </h3>
                        <h4 style="color: #FFA500; margin-bottom: 1rem;">工作方法的新发现</h4>
                        <p style="color: #fff; line-height: 1.6; font-size: 1rem;">
                            不必一味追求自动化和最新技术栈，在开发仅一人的情况下，优先满足核心需求才是王道。告别完美主义！
                        </p>
                        <div style="margin-top: 1rem; padding: 0.8rem; background: rgba(255,215,0,0.2); border-radius: 8px; color: #FFD700; font-weight: bold; text-align: center;">
                            💡 效率星云已记录到航行日志
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 第7页：星光勋章授予仪式 (铺垫) -->
    <div class="page" id="page7">
        <div class="content" style="width: 90%; max-width: 1000px; text-align: center;">
            <h2 class="page-title">全舰广播：致敬我们的伙伴！</h2>
            
            <div style="margin: 3rem auto;">
                <!-- 舰桥主屏幕 -->
                <div class="bridge-screen" style="width: 700px; height: 400px; margin: 0 auto; background: linear-gradient(135deg, #001133, #002244); border: 3px solid #C0C0C0; border-radius: 20px; position: relative; overflow: hidden;">
                    
                    <!-- 屏幕边框装饰 -->
                    <div style="position: absolute; top: 10px; left: 10px; right: 10px; bottom: 10px; border: 1px solid rgba(192,192,192,0.5); border-radius: 15px;"></div>
                    
                    <!-- 滚动赞美文字 -->
                    <div class="praise-scroll" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 90%; height: 80%; overflow: hidden;">
                        <div id="praisesContainer" style="position: relative; height: 100%;">
                            <div class="praise-item" style="position: absolute; width: 100%; opacity: 0; transition: opacity 1s ease; padding: 2rem; text-align: center;">
                                <div style="background: rgba(255,215,0,0.1); padding: 1.5rem; border-radius: 15px; border: 1px solid rgba(255,215,0,0.3);">
                                    <div style="color: #FFD700; font-size: 1.4rem; margin-bottom: 0.5rem;">🌟</div>
                                    <div style="color: #fff; font-size: 1.2rem; line-height: 1.6;">"为CTO送上小红花"</div>
                                    <div style="color: rgba(255,255,255,0.6); font-size: 0.9rem; margin-top: 1rem;">- 来自匿名船员</div>
                                </div>
                            </div>
                            
                            <div class="praise-item" style="position: absolute; width: 100%; opacity: 0; transition: opacity 1s ease; padding: 2rem; text-align: center;">
                                <div style="background: rgba(255,215,0,0.1); padding: 1.5rem; border-radius: 15px; border: 1px solid rgba(255,215,0,0.3);">
                                    <div style="color: #FFD700; font-size: 1.4rem; margin-bottom: 0.5rem;">💫</div>
                                    <div style="color: #fff; font-size: 1.2rem; line-height: 1.6;">"彩排时向云一直告诉我DJ的一些技巧"</div>
                                    <div style="color: rgba(255,255,255,0.6); font-size: 0.9rem; margin-top: 1rem;">- 来自匿名船员</div>
                                </div>
                            </div>
                            
                            <div class="praise-item" style="position: absolute; width: 100%; opacity: 0; transition: opacity 1s ease; padding: 2rem; text-align: center;">
                                <div style="background: rgba(255,215,0,0.1); padding: 1.5rem; border-radius: 15px; border: 1px solid rgba(255,215,0,0.3);">
                                    <div style="color: #FFD700; font-size: 1.4rem; margin-bottom: 0.5rem;">⭐</div>
                                    <div style="color: #fff; font-size: 1.2rem; line-height: 1.6;">"为大梁送上小红花"</div>
                                    <div style="color: rgba(255,255,255,0.6); font-size: 0.9rem; margin-top: 1rem;">- 来自匿名船员</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 状态指示器 -->
                    <div style="position: absolute; bottom: 15px; left: 20px; color: #00FF00; font-family: 'Orbitron'; font-size: 0.8rem;">
                        ● REC  |  广播状态: 活跃
                    </div>
                    <div style="position: absolute; bottom: 15px; right: 20px; color: #FFD700; font-family: 'Orbitron'; font-size: 0.8rem;">
                        信号强度: ████▌
                    </div>
                </div>
                
                <div style="margin-top: 2rem; color: rgba(255,255,255,0.7); font-size: 1.1rem;">
                    舰桥通讯系统正在播放来自全体船员的感谢信息...
                </div>
            </div>
        </div>
    </div>
    
    <!-- 第8页：星光勋章授予仪式 (核心) -->
    <div class="page" id="page8">
        <div class="content" style="width: 90%; max-width: 1000px; text-align: center;">
            <div id="awardTitle" style="opacity: 0; margin-bottom: 3rem;">
                <h2 style="font-family: 'Orbitron'; font-size: clamp(1.5rem, 4vw, 3rem); color: #FFD700; margin-bottom: 1rem;">
                    本航程的"星光勋章"获得者是...
                </h2>
            </div>
            
            <div id="nameReveal" style="margin: 3rem auto; position: relative; width: 600px; height: 300px;">
                <!-- 所有船员名字 -->
                <div class="crew-names" style="display: flex; flex-wrap: wrap; gap: 2rem; justify-content: center; align-items: center; height: 100%;">
                    <div class="crew-name" data-name="赵明" style="padding: 1rem 2rem; border: 2px solid rgba(255,255,255,0.3); border-radius: 10px; color: rgba(255,255,255,0.6); transition: all 0.8s ease; font-family: 'Orbitron'; font-size: 1.2rem;">赵明</div>
                    <div class="crew-name" data-name="马佳平" style="padding: 1rem 2rem; border: 2px solid rgba(255,255,255,0.3); border-radius: 10px; color: rgba(255,255,255,0.6); transition: all 0.8s ease; font-family: 'Orbitron'; font-size: 1.2rem;">马佳平</div>
                    <div class="crew-name" data-name="蔡庆强" style="padding: 1rem 2rem; border: 2px solid rgba(255,255,255,0.3); border-radius: 10px; color: rgba(255,255,255,0.6); transition: all 0.8s ease; font-family: 'Orbitron'; font-size: 1.2rem;">蔡庆强</div>
                    <div class="crew-name" data-name="陈柄宏" style="padding: 1rem 2rem; border: 2px solid rgba(255,255,255,0.3); border-radius: 10px; color: rgba(255,255,255,0.6); transition: all 0.8s ease; font-family: 'Orbitron'; font-size: 1.2rem;">陈柄宏</div>
                    <div class="crew-name" data-name="梁金隆" style="padding: 1rem 2rem; border: 2px solid rgba(255,255,255,0.3); border-radius: 10px; color: rgba(255,255,255,0.6); transition: all 0.8s ease; font-family: 'Orbitron'; font-size: 1.2rem;">梁金隆</div>
                    <div class="crew-name" data-name="盛捷" style="padding: 1rem 2rem; border: 2px solid rgba(255,255,255,0.3); border-radius: 10px; color: rgba(255,255,255,0.6); transition: all 0.8s ease; font-family: 'Orbitron'; font-size: 1.2rem;">盛捷</div>
                    <div class="crew-name winner" data-name="向云" style="padding: 1rem 2rem; border: 2px solid rgba(255,255,255,0.3); border-radius: 10px; color: rgba(255,255,255,0.6); transition: all 0.8s ease; font-family: 'Orbitron'; font-size: 1.2rem; position: relative;">向云</div>
                </div>
                
                <!-- 金色光束 -->
                <div class="light-beams" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;">
                    <div class="light-beam beam1" style="position: absolute; width: 4px; height: 200px; background: linear-gradient(to bottom, #FFD700, transparent); opacity: 0; transform-origin: bottom; animation-duration: 2s; animation-fill-mode: forwards;"></div>
                    <div class="light-beam beam2" style="position: absolute; width: 4px; height: 200px; background: linear-gradient(to bottom, #FFD700, transparent); opacity: 0; transform-origin: bottom; animation-duration: 2s; animation-fill-mode: forwards; animation-delay: 0.5s;"></div>
                    <div class="light-beam beam3" style="position: absolute; width: 4px; height: 200px; background: linear-gradient(to bottom, #FFD700, transparent); opacity: 0; transform-origin: bottom; animation-duration: 2s; animation-fill-mode: forwards; animation-delay: 1s;"></div>
                </div>
            </div>
            
            <!-- 勋章显示 -->
            <div id="medalDisplay" style="opacity: 0; margin-top: 2rem;">
                <div style="width: 150px; height: 150px; margin: 0 auto; position: relative;">
                    <div class="medal" style="width: 100%; height: 100%; background: radial-gradient(circle, #FFD700, #FFA500, #FF6347); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 3rem; color: #fff; text-shadow: 2px 2px 4px rgba(0,0,0,0.5); animation: medal-shine 3s infinite;">
                        ⭐
                    </div>
                    <div style="position: absolute; top: -10px; left: -10px; right: -10px; bottom: -10px; border: 3px solid #FFD700; border-radius: 50%; animation: medal-glow 2s infinite;"></div>
                </div>
                
                <h3 style="color: #FFD700; font-family: 'Orbitron'; font-size: 2rem; margin: 1.5rem 0; text-shadow: 0 0 10px rgba(255,215,0,0.5);">
                    向云 - 团队最闪亮的星！
                </h3>
                
                <div class="tribute-text" style="max-width: 600px; margin: 0 auto; padding: 2rem; background: rgba(255,215,0,0.1); border: 1px solid rgba(255,215,0,0.3); border-radius: 15px;">
                    <p style="color: #fff; font-size: 1.2rem; line-height: 1.8; margin-bottom: 1rem;">
                        感谢你，在关键时刻给予的专业指导与支持！
                    </p>
                    <p style="color: rgba(255,255,255,0.8); font-size: 1rem; line-height: 1.6;">
                        你的技术专长和无私帮助，让整个团队在星际航行中避免了无数困难，是当之无愧的星光守护者！
                    </p>
                </div>
            </div>
            
            <!-- 启动仪式按钮 -->
            <button id="startCeremony" class="control-btn" style="margin-top: 3rem; padding: 15px 30px; font-size: 1.1rem; background: linear-gradient(45deg, #FFD700, #FFA500); color: #000; font-weight: bold;">
                🎖️ 开始授勋仪式
            </button>
        </div>
    </div>
    
    <!-- 第9页：任务完成 -->
    <div class="page" id="page9">
        <div class="content" style="width: 90%; max-width: 1000px; text-align: center;">
            <h2 class="page-title">任务完成，成功返航！</h2>
            
            <div style="margin: 3rem auto;">
                <!-- 任务完成度仪表盘 -->
                <div class="mission-dashboard" style="width: 300px; height: 300px; margin: 0 auto 3rem; position: relative;">
                    <svg width="300" height="300" style="transform: rotate(-90deg);">
                        <circle cx="150" cy="150" r="120" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="8"/>
                        <circle cx="150" cy="150" r="120" fill="none" stroke="url(#progressGradient)" stroke-width="8" 
                                stroke-dasharray="754" stroke-dashoffset="151" stroke-linecap="round">
                            <animate attributeName="stroke-dashoffset" values="754;151" dur="3s" fill="freeze"/>
                        </circle>
                        
                        <defs>
                            <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#FF6347;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                    </svg>
                    
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                        <div style="color: #FFD700; font-family: 'Orbitron'; font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">
                            4.5
                        </div>
                        <div style="color: #fff; font-size: 1.2rem;">/ 5</div>
                        <div style="color: rgba(255,255,255,0.7); font-size: 0.9rem; margin-top: 0.5rem;">任务完成度</div>
                    </div>
                </div>
                
                <!-- 返航动画 -->
                <div class="return-journey" style="margin: 3rem 0; position: relative; width: 800px; height: 200px; margin: 0 auto;">
                    <!-- 空间站 -->
                    <div class="space-station" style="position: absolute; right: 50px; top: 50%; transform: translateY(-50%); width: 150px; height: 100px;">
                        <div style="background: linear-gradient(45deg, #C0C0C0, #E5E5E5); width: 100%; height: 100%; clip-path: polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%); position: relative;">
                            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 60%; height: 60%; background: rgba(0,255,255,0.3); border-radius: 50%;"></div>
                            <div style="position: absolute; top: 20%; left: 20%; width: 10px; height: 10px; background: #FFD700; border-radius: 50%; animation: station-light 2s infinite;"></div>
                            <div style="position: absolute; top: 70%; right: 20%; width: 8px; height: 8px; background: #00FF00; border-radius: 50%; animation: station-light 2s infinite 0.5s;"></div>
                        </div>
                    </div>
                    
                    <!-- 守护者号返航 -->
                    <div class="returning-ship" style="position: absolute; left: 0; top: 50%; transform: translateY(-50%); width: 120px; height: 60px; animation: return-flight 6s ease-in-out infinite;">
                        <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #C0C0C0, #E5E5E5); clip-path: polygon(0 50%, 15% 0%, 85% 0%, 100% 50%, 85% 100%, 15% 100%); position: relative;">
                            <div style="position: absolute; right: -20px; top: 50%; transform: translateY(-50%); width: 30px; height: 8px; background: linear-gradient(90deg, #00BFFF, transparent); border-radius: 4px; filter: blur(2px);"></div>
                        </div>
                    </div>
                    
                    <!-- 航行轨迹 -->
                    <svg width="800" height="200" style="position: absolute; top: 0; left: 0; pointer-events: none;">
                        <path d="M 60 100 Q 300 50 650 100" stroke="rgba(255,215,0,0.4)" stroke-width="2" fill="none" stroke-dasharray="10,5">
                            <animate attributeName="stroke-dasharray" values="0,1000;1000,0" dur="4s" repeatCount="indefinite"/>
                        </path>
                    </svg>
                </div>
                
                <!-- 最终结语 -->
                <div class="final-message" style="margin-top: 3rem;">
                    <div class="card" style="max-width: 600px; margin: 0 auto; background: rgba(255,215,0,0.1); border-color: #FFD700;">
                        <h3 style="color: #FFD700; font-family: 'Orbitron'; margin-bottom: 1.5rem; font-size: 1.5rem;">
                            🚀 任务总结
                        </h3>
                        <div style="color: #fff; text-align: left; line-height: 1.8;">
                            <div style="margin-bottom: 1rem;">
                                ✅ 成功完成福建大学生安全知识竞赛<br>
                                ✅ 团队协作顺利，各司其职<br>
                                ✅ 克服了所有技术挑战<br>
                                ✅ 获得了宝贵的经验和友谊
                            </div>
                        </div>
                        
                        <div style="margin-top: 2rem; padding: 1.5rem; background: rgba(255,215,0,0.2); border-radius: 10px; text-align: center;">
                            <h4 style="color: #FFD700; margin-bottom: 1rem; font-size: 1.3rem;">
                                感谢每一位船员的付出
                            </h4>
                            <p style="color: #fff; font-size: 1.1rem; margin-bottom: 1rem;">
                                期待下一次起航！
                            </p>
                            <div style="color: #FFA500; font-family: 'Orbitron'; font-weight: bold;">
                                ⭐ 守护者号航行日志 - 完 ⭐
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 页面数据
        const pages = [
            { id: 1, title: "封面" },
            { id: 2, title: "舰桥船员" },
            { id: 3, title: "航行日志" },
            { id: 4, title: "穿越小行星带" },
            { id: 5, title: "来自过去的通讯" },
            { id: 6, title: "啊哈！时刻" },
            { id: 7, title: "星光勋章授予仪式 (铺垫)" },
            { id: 8, title: "星光勋章授予仪式 (核心)" },
            { id: 9, title: "任务完成" }
        ];
        
        let currentPage = 1;
        const totalPages = 9;
        
        // 导航功能
        function showPage(pageNum) {
            // 隐藏当前页面
            const currentPageEl = document.querySelector('.page.active');
            if (currentPageEl) {
                currentPageEl.classList.remove('active');
                currentPageEl.classList.add('prev');
            }
            
            // 显示目标页面
            setTimeout(() => {
                const targetPage = document.getElementById(`page${pageNum}`);
                if (targetPage) {
                    // 移除所有页面的活动状态
                    document.querySelectorAll('.page').forEach(page => {
                        page.classList.remove('active', 'prev');
                    });
                    
                    // 激活目标页面
                    targetPage.classList.add('active');
                    
                    // 更新导航点
                    document.querySelectorAll('.nav-dot').forEach((dot, index) => {
                        dot.classList.toggle('active', index + 1 === pageNum);
                    });
                    
                    // 更新按钮状态
                    document.getElementById('prevBtn').disabled = pageNum === 1;
                    document.getElementById('nextBtn').disabled = pageNum === totalPages;
                    
                    currentPage = pageNum;
                }
            }, 100);
        }
        
        // 事件监听器
        document.getElementById('nextBtn').addEventListener('click', () => {
            if (currentPage < totalPages) {
                showPage(currentPage + 1);
            }
        });
        
        document.getElementById('prevBtn').addEventListener('click', () => {
            if (currentPage > 1) {
                showPage(currentPage - 1);
            }
        });
        
        // 导航点点击
        document.querySelectorAll('.nav-dot').forEach((dot, index) => {
            dot.addEventListener('click', () => {
                showPage(index + 1);
            });
        });
        
        // 键盘导航
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft' && currentPage > 1) {
                showPage(currentPage - 1);
            } else if (e.key === 'ArrowRight' && currentPage < totalPages) {
                showPage(currentPage + 1);
            }
        });
        
        // 船员卡片hover效果
        document.querySelectorAll('.crew-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                const description = card.querySelector('.member-description');
                description.style.opacity = '1';
                description.style.height = 'auto';
                description.style.marginTop = '1rem';
            });
            
            card.addEventListener('mouseleave', () => {
                const description = card.querySelector('.member-description');
                description.style.opacity = '0';
                description.style.height = '0';
                description.style.marginTop = '0';
            });
        });
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 创建剩余页面的HTML结构
            createRemainingPages();
        });
        
        function createRemainingPages() {
            initializePage3(); // 航行日志
            initializePage4(); // 小行星带
            initializePage5(); // 过去的通讯
            initializePage6(); // 啊哈时刻
            initializePage7(); // 勋章铺垫
            initializePage8(); // 勋章仪式
            console.log('所有页面初始化完成');
        }
        
        // 第3页：航行日志交互
        function initializePage3() {
            const memories = {
                1: '遭遇"有问必答"的挑战题，考验了我们的数据处理能力。那时团队紧密配合，每个人都展现出了专业素养。',
                2: '与福建医科大学代表队初次接触，从他们认真的提问中感受到了赛事的价值。这次交流让我们明白了知识竞赛的真正意义。',
                3: '一个技术插曲：用最简单的方式，实现了完美的PPT无痕播放。有时候，简单的解决方案往往是最有效的。'
            };
            
            document.querySelectorAll('.star-cluster').forEach(cluster => {
                cluster.addEventListener('click', (e) => {
                    const memoryId = e.target.getAttribute('data-memory');
                    const memoryDisplay = document.getElementById('memoryDisplay');
                    const memoryText = document.getElementById('memoryText');
                    
                    memoryText.textContent = memories[memoryId];
                    memoryDisplay.style.opacity = '1';
                    
                    // 高亮点击的星团
                    document.querySelectorAll('.star-cluster').forEach(c => {
                        c.style.filter = 'brightness(0.5)';
                    });
                    e.target.style.filter = 'brightness(1.5)';
                });
            });
        }
        
        // 第4页：小行星带挑战
        function initializePage4() {
            const solutions = {
                1: '建立了详细的Excel表格系统，实时跟踪每支队伍的得分情况，确保计分准确无误。',
                2: '最终选择了《星际争霸》的背景音乐，既符合比赛氛围又不会干扰选手思考。',
                3: '果断切换回纯软件方案，保障了终极PK环节的稳定性！硬件有时不如软件可靠。'
            };
            
            document.querySelectorAll('.asteroid').forEach(asteroid => {
                asteroid.addEventListener('click', (e) => {
                    const challengeId = e.currentTarget.getAttribute('data-challenge');
                    const solutionDisplay = document.getElementById('solutionDisplay');
                    const solutionText = document.getElementById('solutionText');
                    const spaceshipEvasion = document.querySelector('.spaceship-evasion div');
                    
                    // 播放规避动画
                    spaceshipEvasion.style.transform = 'translateX(50px)';
                    
                    setTimeout(() => {
                        spaceshipEvasion.style.transform = 'translateX(0)';
                        solutionText.textContent = solutions[challengeId];
                        solutionDisplay.style.opacity = '1';
                    }, 800);
                });
            });
        }
        
        // 第5页：过去的通讯
        function initializePage5() {
            const messages = [
                '不要紧张，这只是一次很小的比赛。',
                '做好份内事，学会拒绝。',
                '别给自己太大压力，人生有很多容错率。'
            ];
            
            let currentMessageIndex = -1;
            const messageDisplay = document.getElementById('currentMessage');
            const nextBtn = document.getElementById('nextMessage');
            
            function showNextMessage() {
                currentMessageIndex = (currentMessageIndex + 1) % messages.length;
                messageDisplay.textContent = messages[currentMessageIndex];
                messageDisplay.style.opacity = '0';
                
                setTimeout(() => {
                    messageDisplay.style.opacity = '1';
                }, 300);
            }
            
            // 点击开始或下一条
            messageDisplay.addEventListener('click', showNextMessage);
            nextBtn.addEventListener('click', showNextMessage);
        }
        
        // 第6页：雷达扫描发现
        function initializePage6() {
            const scanProgress = document.getElementById('scanProgress');
            const nebulaTarget = document.getElementById('nebulaTarget');
            const discoveryResult = document.getElementById('discoveryResult');
            
            let progress = 0;
            const scanInterval = setInterval(() => {
                progress += Math.random() * 10;
                scanProgress.textContent = Math.min(100, Math.floor(progress)) + '%';
                
                if (progress >= 100) {
                    clearInterval(scanInterval);
                    scanProgress.textContent = '100% - 发现异常信号！';
                    
                    setTimeout(() => {
                        nebulaTarget.style.opacity = '1';
                    }, 500);
                }
            }, 200);
            
            nebulaTarget.addEventListener('click', () => {
                discoveryResult.style.opacity = '1';
                discoveryResult.style.right = '50px';
            });
        }
        
        // 第7页：赞美滚动
        function initializePage7() {
            const praiseItems = document.querySelectorAll('.praise-item');
            let currentPraise = 0;
            
            function showNextPraise() {
                praiseItems.forEach(item => item.style.opacity = '0');
                praiseItems[currentPraise].style.opacity = '1';
                currentPraise = (currentPraise + 1) % praiseItems.length;
            }
            
            // 初始显示
            showNextPraise();
            
            // 每3秒切换一次
            setInterval(showNextPraise, 3000);
        }
        
        // 第8页：授勋仪式
        function initializePage8() {
            const startBtn = document.getElementById('startCeremony');
            const awardTitle = document.getElementById('awardTitle');
            const nameReveal = document.getElementById('nameReveal');
            const medalDisplay = document.getElementById('medalDisplay');
            
            startBtn.addEventListener('click', () => {
                startBtn.style.display = 'none';
                
                // 第一步：显示标题
                setTimeout(() => {
                    awardTitle.style.opacity = '1';
                }, 500);
                
                // 第二步：光束照射向云
                setTimeout(() => {
                    const winnerName = document.querySelector('.crew-name.winner');
                    const beams = document.querySelectorAll('.light-beam');
                    const winnerRect = winnerName.getBoundingClientRect();
                    const containerRect = nameReveal.getBoundingClientRect();
                    
                    // 计算向云的位置
                    const winnerX = winnerRect.left - containerRect.left + winnerRect.width / 2;
                    const winnerY = winnerRect.top - containerRect.top + winnerRect.height / 2;
                    
                    // 定位光束
                    beams[0].style.left = (winnerX - 30) + 'px';
                    beams[0].style.top = '0px';
                    beams[0].style.animationName = 'light-beam-appear';
                    
                    beams[1].style.left = winnerX + 'px';
                    beams[1].style.top = '0px';
                    beams[1].style.animationName = 'light-beam-appear';
                    
                    beams[2].style.left = (winnerX + 30) + 'px';
                    beams[2].style.top = '0px';
                    beams[2].style.animationName = 'light-beam-appear';
                    
                    // 高亮向云的名字
                    setTimeout(() => {
                        winnerName.style.background = 'linear-gradient(45deg, #FFD700, #FFA500)';
                        winnerName.style.color = '#000';
                        winnerName.style.boxShadow = '0 0 30px rgba(255,215,0,0.8)';
                        winnerName.style.transform = 'scale(1.2)';
                        
                        // 其他名字变暗
                        document.querySelectorAll('.crew-name:not(.winner)').forEach(name => {
                            name.style.opacity = '0.3';
                        });
                    }, 1500);
                }, 2000);
                
                // 第三步：显示勋章
                setTimeout(() => {
                    medalDisplay.style.opacity = '1';
                }, 5000);
            });
        }
        
        // 页面切换时的特殊处理
        const originalShowPage = showPage;
        showPage = function(pageNum) {
            originalShowPage(pageNum);
            
            // 特殊页面处理
            if (pageNum === 6) {
                // 重新启动雷达扫描
                setTimeout(() => {
                    if (document.getElementById(`page${pageNum}`).classList.contains('active')) {
                        initializePage6();
                    }
                }, 800);
            }
        };
    </script>
</body>
</html>