// 交互脚本：星光守护者计划
// 主题：未来科技感星际探索，银白与金色UI

(function () {
  const PAGES_TOTAL = 9;
  let currentPage = 1;

  const qs = (sel, el = document) => el.querySelector(sel);
  const qsa = (sel, el = document) => Array.from(el.querySelectorAll(sel));

  function setActivePage(index) {
    currentPage = Math.max(1, Math.min(PAGES_TOTAL, index));
    const pages = qsa('.page');
    pages.forEach((p, i) => {
      p.classList.toggle('active', i === currentPage - 1);
    });
    updateIndicator();
    onPageEnter(currentPage);
  }

  function updateIndicator() {
    const indicator = qs('#pageIndicator');
    indicator.textContent = `${currentPage} / ${PAGES_TOTAL}`;
  }

  // 导航按钮与键盘
  function setupNavigation() {
    const prevBtn = qs('#prevBtn');
    const nextBtn = qs('#nextBtn');
    prevBtn.addEventListener('click', () => setActivePage(currentPage - 1));
    nextBtn.addEventListener('click', () => setActivePage(currentPage + 1));

    window.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowRight' || e.key.toLowerCase() === 'l') setActivePage(currentPage + 1);
      if (e.key === 'ArrowLeft' || e.key.toLowerCase() === 'h') setActivePage(currentPage - 1);
    });
  }

  // 星野背景
  function setupStarfield() {
    const canvas = qs('#starfield');
    const ctx = canvas.getContext('2d');
    let width = (canvas.width = window.innerWidth);
    let height = (canvas.height = window.innerHeight);
    const numStars = Math.min(240, Math.floor((width * height) / 14000));
    const stars = [];
    let warp = false;

    function randomStar() {
      return {
        x: Math.random() * width,
        y: Math.random() * height,
        z: Math.random() * 0.8 + 0.2,
        s: Math.random() * 1.2 + 0.2,
      };
    }

    for (let i = 0; i < numStars; i++) stars.push(randomStar());

    function draw() {
      ctx.clearRect(0, 0, width, height);
      for (let i = 0; i < stars.length; i++) {
        const star = stars[i];
        const speed = warp ? (1.2 + star.z * 3) : (0.2 + star.z * 0.8);
        star.x += speed;
        if (star.x > width + 10) {
          star.x = -10;
          star.y = Math.random() * height;
          star.z = Math.random() * 0.8 + 0.2;
        }
        const alpha = 0.5 + Math.min(0.5, star.z);
        ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
        ctx.fillRect(star.x, star.y, star.s, star.s);
        if (warp) {
          ctx.fillStyle = `rgba(88, 243, 255, ${0.25 + star.z * 0.4})`;
          ctx.fillRect(star.x - 4 - star.z * 8, star.y, 4 + star.z * 8, 1);
        }
      }
      requestAnimationFrame(draw);
    }

    function onResize() {
      width = canvas.width = window.innerWidth;
      height = canvas.height = window.innerHeight;
    }

    window.addEventListener('resize', onResize);
    requestAnimationFrame(draw);

    return {
      enableWarp: () => (warp = true),
      disableWarp: () => (warp = false),
    };
  }

  // 页面进入时的钩子
  function onPageEnter(pageIndex) {
    switch (pageIndex) {
      case 1:
        starfield.enableWarp();
        break;
      default:
        starfield.disableWarp();
        break;
    }

    if (pageIndex === 3) initPage3();
    if (pageIndex === 4) initPage4();
    if (pageIndex === 5) initPage5();
    if (pageIndex === 6) initPage6();
    if (pageIndex === 8) initPage8();
    if (pageIndex === 9) initPage9();
  }

  // 第3页：航行日志
  function initPage3() {
    const panel = qs('#page3 .log-panel .log-content');
    if (!panel) return;

    const map = {
      c1: '星团1：遭遇“有问必答”的挑战题，考验了我们的数据处理能力。',
      c2: '星团2：与福建医科大学代表队初次接触，从他们认真的提问中感受到了赛事的价值。',
      c3: '星团3：一个技术插曲：用最简单的方式，实现了完美的PPT无痕播放。',
    };

    qsa('#page3 .cluster').forEach((node) => {
      node.addEventListener('click', () => {
        const key = node.getAttribute('data-key');
        panel.textContent = map[key] || '点击任一星团以展开记忆';
      });
    });
  }

  // 第4页：挑战与规避
  function initPage4() {
    const ship = qs('#page4 .ship-evade');
    const solutionBox = qs('#page4 .solution');
    if (!ship || !solutionBox) return;

    const solutions = {
      score: '解决方案：重新设计计分表结构，增加冗余校验与人工确认环节。',
      music: '解决方案：建立临时素材库，选取与环节强度相匹配的循环音轨。',
      timer: '解决方案：果断切换回纯软件方案，保障了终极PK环节的稳定性！',
    };

    qsa('#page4 .meteor').forEach((m) => {
      m.addEventListener('click', () => {
        // 规避动画
        ship.classList.remove('evading');
        void ship.offsetWidth; // 触发重排以重放动画
        ship.classList.add('evading');
        const key = m.getAttribute('data-problem');
        solutionBox.textContent = solutions[key] || '';
      });
    });
  }

  // 第5页：全息通讯
  let page5Started = false;
  function initPage5() {
    if (page5Started) return;
    page5Started = true;
    const lines = qsa('#page5 .holo-line');
    lines.forEach((line) => (line.classList.remove('show')));
    // 依次播放
    lines.forEach((line, idx) => {
      setTimeout(() => line.classList.add('show'), 500 + idx * 900);
    });
  }

  // 第6页：雷达与发现
  let page6Bound = false;
  function initPage6() {
    if (page6Bound) return;
    page6Bound = true;
    const btn = qs('#page6 .nebula-target');
    const card = qs('#page6 .discovery-card');
    btn.addEventListener('click', () => {
      card.classList.add('show');
    });
  }

  // 第8页：颁奖动画
  function initPage8() {
    const beams = qsa('#page8 .beam');
    const medalWrap = qs('#page8 .medal-wrap');
    // 重置
    beams.forEach((b) => {
      b.classList.remove('fire');
      void b.offsetWidth;
      b.classList.add('fire');
    });
    medalWrap.classList.remove('show');
    // 延时展示勋章
    setTimeout(() => medalWrap.classList.add('show'), 900);
  }

  // 第9页：仪表盘与对接
  function initPage9() {
    const gauge = qs('#page9 .gauge');
    const value = parseFloat(gauge.getAttribute('data-value') || '0.9');
    const deg = Math.round(360 * value);
    gauge.style.background = `conic-gradient(var(--gold) 0deg, var(--gold) ${deg}deg, rgba(255,255,255,0.08) ${deg}deg)`;
    const needle = qs('#page9 .gauge-needle');
    needle.style.transition = 'transform 1.2s cubic-bezier(.2,.8,.2,1)';
    needle.style.transform = `rotate(${deg - 90}deg)`;

    const ship = qs('#page9 .ship-dock');
    ship.classList.remove('docking');
    void ship.offsetWidth;
    ship.classList.add('docking');
  }

  // 初始化
  setupNavigation();
  const starfield = setupStarfield();
  setActivePage(1);
})();


