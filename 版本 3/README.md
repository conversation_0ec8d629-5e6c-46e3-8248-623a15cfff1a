# 任务报告：“星光守护者”计划 - 互动网页

这是一个为项目复盘制作的单页互动网站（9 个分页），主题为“未来科技感的星际探索”，视觉风格采用深邃宇宙背景、银白与金色 UI 元素，并包含多处交互动画。

## 目录结构

- `index.html` - 页面结构与 9 个分页内容
- `styles.css` - 全站样式与动画
- `script.js` - 交互逻辑与星野背景

## 使用方式

- 直接用浏览器打开 `index.html` 即可。
- 使用顶部“上一页 / 下一页”按钮或键盘方向键进行分页切换。

## 分页概览

1. 封面：守护者号从超空间出现，目标蓝色星球（带发光的“福建”轮廓）。
2. 舰桥船员：6 张全息档案卡，鼠标悬停显示角色描述原文。
3. 航行日志：星图上的三个可点击“高亮星团”，点击显示对应记忆。
4. 小行星带：点击左侧红色“陨石”，右侧触发规避动画并显示解决方案。
5. 来自过去的通讯：全息投影依次播放 3 条建议。
6. “啊哈！”时刻：雷达扫描锁定“效率星云”，点击展开发现卡片。
7. 颁奖铺垫：舰桥主屏幕滚动播放匿名赞美。
8. 颁奖动画：三道金色光束汇聚到“向云”，生成“星光勋章”。
9. 任务完成：仪表盘显示“4.5/5”，飞船对接空间站，显示结束语。

## 自定义与扩展

- 可在 `styles.css` 顶部修改主题色（银白/金色等）。
- 在 `index.html` 的各页区块中替换或添加内容文本。
- 如需增加音效，可在 `script.js` 中对应页进入时播放音频。

---

版权说明：本页面所用的图形与动画均为 CSS/Canvas 程序化生成，无需额外素材文件。


