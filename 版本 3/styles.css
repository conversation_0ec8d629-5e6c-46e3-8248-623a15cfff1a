/* 全局主题与变量 */
:root {
  --bg-deep: #070814;
  --bg-space: #0a0c1a;
  --silver: #c0c7d1;
  --silver-soft: #aab3c0;
  --gold: #d4af37;
  --gold-bright: #ffd86b;
  --cyan: #58f3ff;
  --blue: #3a8eff;
  --red: #ff4d6d;
  --green: #37ffb1;
  --text: #e6edf5;
  --muted: #9aa7b5;
  --panel: rgba(255, 255, 255, 0.06);
  --panel-strong: rgba(255, 255, 255, 0.12);
  --blur: saturate(120%) blur(8px);
}

* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  background: linear-gradient(180deg, var(--bg-deep), var(--bg-space));
  color: var(--text);
  font-family: 'Noto Sans SC', 'Microsoft YaHei', 'PingFang SC', system-ui, -apple-system, <PERSON><PERSON><PERSON>, Robot<PERSON>, Ubunt<PERSON>, Can<PERSON>ell, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  margin: 0;
}

h1, h2, h3, .brand, .section-title {
  font-family: 'Orbitron', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
  letter-spacing: 0.02em;
}

#starfield {
  position: fixed;
  inset: 0;
  z-index: -2;
  width: 100vw;
  height: 100vh;
  display: block;
}

#nebula-overlay {
  position: fixed;
  inset: 0;
  z-index: -1;
  pointer-events: none;
  background:
    radial-gradient(1200px 800px at 70% 20%, rgba(30, 80, 200, 0.25), transparent 60%),
    radial-gradient(1200px 800px at 30% 80%, rgba(140, 30, 180, 0.2), transparent 60%),
    radial-gradient(800px 600px at 10% 10%, rgba(0, 240, 255, 0.12), transparent 70%);
  filter: blur(2px) saturate(120%);
}

#top-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 18px;
  background: linear-gradient(180deg, rgba(10,12,26,0.9), rgba(10,12,26,0.4));
  border-bottom: 1px solid rgba(255,255,255,0.08);
  backdrop-filter: var(--blur);
  z-index: 10;
}

.brand {
  color: var(--silver);
  font-weight: 700;
}

.pager {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-btn {
  background: linear-gradient(180deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02));
  color: var(--text);
  border: 1px solid rgba(255,255,255,0.18);
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  border-color: var(--gold);
  color: var(--gold);
  box-shadow: 0 0 12px rgba(212, 175, 55, 0.35) inset, 0 0 18px rgba(212, 175, 55, 0.25);
}

.indicator {
  color: var(--muted);
  font-weight: 600;
}

#pages {
  position: relative;
  height: 100vh;
  overflow: hidden;
}

.page {
  position: absolute;
  inset: 0;
  padding-top: 64px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 450ms ease, transform 600ms ease;
  transform: translateY(10px) scale(0.99);
}

.page.active {
  opacity: 1;
  pointer-events: auto;
  transform: translateY(0) scale(1);
}

.content {
  height: 100%;
  padding: 24px min(6vw, 48px) 32px;
}

.content.center {
  display: grid;
  grid-template-rows: auto 1fr auto;
  place-items: center;
  text-align: center;
}

.title {
  font-size: clamp(28px, 3.4vw, 56px);
  margin: 12px 0 8px;
  color: var(--gold);
  text-shadow: 0 0 16px rgba(212, 175, 55, 0.35);
}

.subtitle {
  font-size: clamp(14px, 1.4vw, 20px);
  color: var(--silver);
  opacity: 0.9;
}

.section-title {
  font-size: clamp(22px, 2.4vw, 36px);
  margin: 8px 0 22px;
  color: var(--gold-bright);
}

/* 封面场景 */
.cover .scene {
  position: relative;
  width: min(1000px, 86vw);
  height: min(520px, 60vh);
  margin-top: 24px;
}

.planet {
  position: absolute;
  right: 6%;
  top: 12%;
  width: min(320px, 40vh);
  aspect-ratio: 1 / 1;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, #6bd3ff, #1c4bff 35%, #0a1570 70%, #001 100%);
  box-shadow: 0 0 120px rgba(59, 135, 255, 0.35), inset -20px -30px 60px rgba(0,0,0,0.6);
  overflow: hidden;
}

.planet .atmosphere {
  position: absolute;
  inset: -10%;
  border-radius: 50%;
  background: radial-gradient(circle at 50% 50%, rgba(120,255,255,0.25), rgba(120,255,255,0) 60%);
  filter: blur(10px);
}

.fujian-outline {
  position: absolute;
  left: 22%;
  top: 38%;
  width: 42%;
  height: 30%;
  background: rgba(88, 243, 255, 0.16);
  border: 2px solid rgba(88, 243, 255, 0.85);
  box-shadow: 0 0 18px rgba(88, 243, 255, 0.9), 0 0 36px rgba(88, 243, 255, 0.45) inset;
  filter: drop-shadow(0 0 10px rgba(88,243,255,0.6));
  clip-path: polygon(5% 30%, 20% 10%, 45% 5%, 70% 15%, 85% 30%, 95% 55%, 78% 80%, 55% 90%, 35% 85%, 20% 70%, 10% 55%);
  animation: outlinePulse 2.2s ease-in-out infinite;
}

@keyframes outlinePulse {
  0%, 100% { opacity: 0.7; transform: translateY(0) scale(1); }
  50% { opacity: 1; transform: translateY(-2px) scale(1.03); }
}

.ship-warp {
  position: absolute;
  left: 8%;
  bottom: 16%;
  width: 90px;
  height: 22px;
  border-radius: 50% 4px 4px 50% / 50% 50% 50% 50%;
  background: linear-gradient(90deg, #9ad3ff, #e0f7ff);
  box-shadow: 0 0 24px rgba(88,243,255,0.55), 0 0 50px rgba(88,243,255,0.35);
}

.ship-warp::before {
  content: '';
  position: absolute;
  right: 80%;
  top: 50%;
  transform: translateY(-50%);
  width: 220px;
  height: 2px;
  background: linear-gradient(90deg, rgba(88,243,255,0), rgba(88,243,255,0.85));
  filter: blur(0.8px);
  animation: warpTrail 2.6s ease-out infinite;
}

@keyframes warpTrail {
  0% { width: 0; opacity: 0.0; }
  20% { width: 260px; opacity: 1; }
  100% { width: 220px; opacity: 0.2; }
}

.trajectory {
  position: absolute;
  left: 14%;
  bottom: 18%;
  width: 46%;
  height: 40%;
  border-left: 2px dashed rgba(212, 175, 55, 0.5);
  border-bottom: 2px dashed rgba(212, 175, 55, 0.5);
  border-radius: 0 0 0 48%/0 0 0 48%;
  filter: drop-shadow(0 0 6px rgba(212,175,55,0.5));
}

.hint {
  color: var(--muted);
  margin-top: 10px;
}

/* 第2页：船员卡片 */
.crew-grid {
  display: grid;
  grid-template-columns: repeat(3, minmax(220px, 1fr));
  gap: 18px;
}

.crew-card {
  position: relative;
  background: linear-gradient(180deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03));
  border: 1px solid rgba(255,255,255,0.18);
  border-radius: 14px;
  padding: 16px;
  min-height: 160px;
  overflow: hidden;
  transition: transform 200ms ease, box-shadow 200ms ease, border-color 200ms ease;
}

.crew-card .holo {
  position: absolute;
  inset: -1px;
  background: radial-gradient(500px 160px at 50% 100%, rgba(88,243,255,0.12), transparent 60%);
  pointer-events: none;
}

.crew-card:hover {
  transform: translateY(-4px) scale(1.01);
  border-color: rgba(212,175,55,0.8);
  box-shadow: 0 0 28px rgba(212,175,55,0.25);
}

.card-header {
  font-weight: 800;
  color: var(--text);
  margin-bottom: 4px;
}

.card-role {
  color: var(--gold);
  font-weight: 700;
}

.card-desc {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 12px 16px;
  background: linear-gradient(180deg, rgba(10,12,26,0), rgba(10,12,26,0.9));
  transform: translateY(100%);
  transition: transform 200ms ease;
  color: var(--silver);
}

.crew-card:hover .card-desc {
  transform: translateY(0);
}

/* 第3页：航行日志星图 */
.starmap {
  position: relative;
  height: min(420px, 52vh);
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 16px;
  background: linear-gradient(180deg, rgba(255,255,255,0.04), rgba(255,255,255,0.02));
  overflow: hidden;
}

.cluster {
  position: absolute;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: radial-gradient(circle, #fff, #9ad3ff 40%, #3a8eff 70%);
  box-shadow: 0 0 24px rgba(58,142,255,0.7), 0 0 48px rgba(58,142,255,0.35);
  border: none;
  cursor: pointer;
  animation: pulse 2.4s ease-in-out infinite;
}

.cluster[data-key="c1"] { left: 16%; top: 34%; }
.cluster[data-key="c2"] { left: 54%; top: 62%; }
.cluster[data-key="c3"] { left: 78%; top: 20%; }

@keyframes pulse { 0%,100%{ transform: scale(1); } 50%{ transform: scale(1.35); } }

.cluster-glow {
  position: absolute;
  inset: 0;
  background: radial-gradient(120px 80px at 16% 34%, rgba(58,142,255,0.16), transparent 70%),
              radial-gradient(140px 100px at 54% 62%, rgba(58,142,255,0.14), transparent 70%),
              radial-gradient(120px 80px at 78% 20%, rgba(58,142,255,0.16), transparent 70%);
  pointer-events: none;
}

.log-panel {
  margin-top: 14px;
  padding: 14px 16px;
  border-radius: 12px;
  background: var(--panel);
  border: 1px solid rgba(255,255,255,0.12);
}

/* 第4页：挑战与解决 */
.two-col {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 16px;
}

.left {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.meteor {
  background: linear-gradient(180deg, rgba(255, 77, 109, 0.2), rgba(255, 77, 109, 0.08));
  color: #fff;
  border: 1px solid rgba(255, 77, 109, 0.6);
  border-radius: 12px;
  padding: 12px;
  text-align: left;
  cursor: pointer;
  box-shadow: 0 0 20px rgba(255, 77, 109, 0.25) inset;
  animation: blink 1.1s steps(2, jump-none) infinite;
}

@keyframes blink { 50% { filter: brightness(1.25); } }

.right { position: relative; }

.evasion-area {
  position: relative;
  height: min(360px, 46vh);
  border-radius: 14px;
  border: 1px dashed rgba(255,255,255,0.24);
  background: radial-gradient(600px 260px at 60% 30%, rgba(255,255,255,0.06), rgba(255,255,255,0.02));
  overflow: hidden;
}

.asteroid-field::before, .asteroid-field::after {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(6px 6px at 20% 20%, rgba(255,77,109,0.5), transparent 60%),
              radial-gradient(8px 8px at 40% 60%, rgba(255,77,109,0.5), transparent 60%),
              radial-gradient(5px 5px at 80% 35%, rgba(255,77,109,0.4), transparent 60%),
              radial-gradient(7px 7px at 70% 80%, rgba(255,77,109,0.5), transparent 60%);
}

.ship-evade {
  position: absolute;
  left: 8%;
  bottom: 12%;
  width: 58px;
  height: 16px;
  border-radius: 50% 4px 4px 50% / 50% 50% 50% 50%;
  background: linear-gradient(90deg, #cfe9ff, #9ad3ff);
  box-shadow: 0 0 16px rgba(88,243,255,0.5);
}

.ship-evade.evading {
  animation: evade 1.8s ease-in-out both;
}

@keyframes evade {
  0% { transform: translate(0, 0) rotate(0); }
  25% { transform: translate(120px, -40px) rotate(-6deg); }
  50% { transform: translate(240px, 6px) rotate(3deg); }
  75% { transform: translate(340px, -24px) rotate(-3deg); }
  100% { transform: translate(440px, 10px) rotate(0); }
}

.solution {
  margin-top: 10px;
  color: var(--silver);
}

/* 第5页：通讯（全息） */
.holo-projector {
  position: relative;
  width: min(800px, 86vw);
  height: min(360px, 44vh);
  margin-top: 16px;
  border-radius: 18px;
  border: 1px solid rgba(88,243,255,0.35);
  background: linear-gradient(180deg, rgba(88,243,255,0.08), rgba(88,243,255,0.03));
  box-shadow: 0 0 28px rgba(88,243,255,0.25) inset, 0 0 22px rgba(88,243,255,0.25);
  overflow: hidden;
}

.holo-projector .emitter {
  position: absolute;
  left: 50%;
  bottom: 10px;
  transform: translateX(-50%);
  width: 120px;
  height: 8px;
  border-radius: 8px;
  background: radial-gradient(120px 18px at 50% 50%, rgba(88,243,255,0.6), rgba(88,243,255,0.1));
  filter: blur(0.6px);
}

.holo-messages {
  position: absolute;
  inset: 16px 16px 36px;
  display: grid;
  align-content: center;
  gap: 16px;
  text-align: center;
}

.holo-line {
  opacity: 0;
  transform: translateY(8px) scale(0.98);
  padding: 10px 14px;
  border-radius: 12px;
  border: 1px solid rgba(88,243,255,0.28);
  background: linear-gradient(180deg, rgba(88,243,255,0.12), rgba(88,243,255,0.04));
  color: #dffbff;
  text-shadow: 0 0 8px rgba(88,243,255,0.6);
}

.holo-line.show {
  animation: holoIn 680ms ease forwards;
}

@keyframes holoIn {
  0% { opacity: 0; transform: translateY(12px) scale(0.96); filter: blur(2px); }
  100% { opacity: 1; transform: translateY(0) scale(1); filter: blur(0); }
}

/* 第6页：雷达扫描 */
.radar-wrap {
  position: relative;
  display: grid;
  grid-template-columns: 260px auto;
  gap: 18px;
  align-items: center;
}

.radar {
  position: relative;
  width: 240px;
  height: 240px;
  border-radius: 50%;
  background: radial-gradient(circle at 50% 50%, rgba(55,255,177,0.16), transparent 60%);
  border: 1px solid rgba(55,255,177,0.35);
  box-shadow: 0 0 18px rgba(55,255,177,0.25) inset;
  overflow: hidden;
}

.radar .sweep {
  position: absolute;
  inset: 0;
  background: conic-gradient(from 0deg, rgba(55,255,177,0.4), rgba(55,255,177,0) 40%);
  animation: sweep 3.2s linear infinite;
}

@keyframes sweep { to { transform: rotate(1turn); } }

.radar .blip {
  position: absolute;
  left: 58%;
  top: 40%;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--green);
  box-shadow: 0 0 18px rgba(55,255,177,0.8), 0 0 42px rgba(55,255,177,0.5);
  animation: blip 1.4s ease-in-out infinite;
}

@keyframes blip { 50% { transform: scale(1.4); } }

.nebula-target {
  justify-self: start;
  background: linear-gradient(180deg, rgba(212,175,55,0.16), rgba(212,175,55,0.06));
  color: var(--gold);
  border: 1px solid rgba(212,175,55,0.6);
  border-radius: 12px;
  padding: 10px 14px;
  cursor: pointer;
}

.discovery-card {
  margin-top: 14px;
  padding: 14px 16px;
  background: var(--panel);
  border: 1px solid rgba(255,255,255,0.12);
  border-radius: 12px;
  display: none;
}

.discovery-card.show { display: block; }

.card-title { color: var(--gold-bright); font-weight: 800; margin-bottom: 6px; }

/* 第7页：广播与跑马灯 */
.bridge-screen {
  position: relative;
  height: min(320px, 40vh);
  border-radius: 14px;
  border: 1px solid rgba(255,255,255,0.14);
  background: linear-gradient(180deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02));
  overflow: hidden;
}

.ticker {
  position: absolute;
  inset: 0;
  display: grid;
  grid-auto-rows: 1fr;
  align-content: start;
  animation: scrollUp 12s linear infinite;
}

.tick {
  padding: 16px;
  border-bottom: 1px dashed rgba(255,255,255,0.12);
  color: var(--silver);
}

@keyframes scrollUp {
  0% { transform: translateY(0); }
  100% { transform: translateY(-50%); }
}

/* 第8页：颁奖动画 */
.name-wall {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 10px;
  margin-bottom: 18px;
}

.name {
  padding: 10px 12px;
  text-align: center;
  background: linear-gradient(180deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02));
  border: 1px solid rgba(255,255,255,0.12);
  border-radius: 10px;
  color: var(--silver);
}

.name.winner {
  color: var(--gold);
  border-color: rgba(212,175,55,0.8);
  box-shadow: 0 0 22px rgba(212,175,55,0.25) inset;
}

.beams { position: relative; height: 0; }
.beam {
  position: absolute;
  width: 36vw;
  max-width: 520px;
  height: 3px;
  background: linear-gradient(90deg, rgba(212,175,55,0), rgba(212,175,55,0.95));
  filter: blur(0.3px);
  opacity: 0;
}

.beam-a { left: 4%; top: -20px; transform-origin: left center; transform: rotate(8deg); }
.beam-b { right: 4%; top: -8px; transform-origin: right center; transform: rotate(-10deg) scaleX(-1); }
.beam-c { left: 16%; top: 12px; transform-origin: left center; transform: rotate(-18deg); }

.beam.fire { animation: beamFire 1.2s ease-out forwards; }

@keyframes beamFire {
  0% { opacity: 0; transform: scaleX(0.2) translateX(-10%); }
  60% { opacity: 1; transform: scaleX(1.05) translateX(0); }
  100% { opacity: 1; transform: scaleX(1) translateX(0); }
}

.medal-wrap {
  display: grid;
  place-items: center;
  gap: 10px;
  margin-top: 10px;
  opacity: 0;
  transform: translateY(8px) scale(0.98);
}

.medal-wrap.show { animation: medalIn 900ms ease forwards 900ms; }

@keyframes medalIn {
  to { opacity: 1; transform: translateY(0) scale(1); }
}

.medal {
  width: 120px;
  aspect-ratio: 1/1;
  border-radius: 50%;
  background: radial-gradient(circle at 35% 35%, #fff6c9, #ffd86b 35%, #d4af37 65%, #6f5a1f 100%);
  box-shadow: 0 0 28px rgba(212,175,55,0.4), 0 0 60px rgba(212,175,55,0.25);
  position: relative;
}

.medal::after {
  content: '';
  position: absolute;
  inset: 12%;
  border-radius: 50%;
  border: 2px solid rgba(255,255,255,0.7);
  box-shadow: inset 0 0 20px rgba(255,255,255,0.6);
}

.medal-caption { text-align: center; }
.medal-title { color: var(--gold-bright); font-weight: 900; }
.medal-desc { color: var(--silver); }

/* 第9页：仪表盘与对接 */
.dashboard { display: grid; place-items: center; margin: 8px 0 16px; }

.gauge {
  width: min(380px, 70vw);
  aspect-ratio: 1/1;
  border-radius: 50%;
  display: grid;
  place-items: center;
  background: conic-gradient(var(--gold) 0deg, var(--gold) 0deg, rgba(255,255,255,0.08) 0deg);
  border: 1px solid rgba(255,255,255,0.14);
}

.gauge-inner {
  width: 74%;
  aspect-ratio: 1/1;
  border-radius: 50%;
  background: radial-gradient(circle at 50% 50%, rgba(255,255,255,0.06), rgba(255,255,255,0.02));
  display: grid;
  place-items: center;
  position: relative;
}

.gauge-needle {
  position: absolute;
  width: 2px;
  height: 45%;
  background: var(--silver);
  transform-origin: bottom center;
  transform: rotate(-90deg);
}

.gauge-label { color: var(--silver); margin-top: 6px; }

.dock-scene {
  position: relative;
  height: 200px;
  display: grid;
  place-items: center;
}

.station {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  border: 2px solid rgba(88,243,255,0.4);
  box-shadow: 0 0 28px rgba(88,243,255,0.25) inset;
  background: radial-gradient(circle at 50% 50%, rgba(88,243,255,0.08), transparent 60%);
}

.ship-dock {
  position: absolute;
  left: 10%;
  width: 50px;
  height: 14px;
  border-radius: 50% 4px 4px 50% / 50% 50% 50% 50%;
  background: linear-gradient(90deg, #cfe9ff, #9ad3ff);
  box-shadow: 0 0 16px rgba(88,243,255,0.5);
}

.ship-dock.docking {
  animation: docking 3s ease-in-out forwards;
}

@keyframes docking {
  0% { transform: translateX(0); opacity: 0.8; }
  70% { transform: translateX(260px); opacity: 1; }
  100% { transform: translateX(320px); opacity: 1; }
}

.end-text { color: var(--silver); margin-top: 8px; }

#footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: grid;
  place-items: center;
  padding: 10px 0 12px;
  color: var(--muted);
  font-size: 12px;
  pointer-events: none;
}

/* 响应式 */
@media (max-width: 900px) {
  .crew-grid { grid-template-columns: repeat(2, minmax(200px, 1fr)); }
  .two-col { grid-template-columns: 1fr; }
  .radar-wrap { grid-template-columns: 1fr; }
}


