/* 全局导航与交互绑定 */
(function(){
  const pagesEl = document.querySelector('.pages');
  const sections = Array.from(document.querySelectorAll('.page'));
  const dots = Array.from(document.querySelectorAll('.pager-dots button'));
  let current = 1;

  function goTo(page){
    page = Math.max(1, Math.min(9, page));
    const target = document.querySelector(`#page-${page}`);
    if(!target) return;
    target.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }

  function updateDotsByScroll(){
    const top = pagesEl.scrollTop;
    let idx = 1;
    for(let i=0;i<sections.length;i++){
      const s = sections[i];
      if(s.offsetTop - top <= window.innerHeight * 0.5) idx = i+1;
    }
    if(idx !== current){
      current = idx;
      dots.forEach(b=>b.setAttribute('aria-selected', String(parseInt(b.dataset.target)===current)));
      if(window.Anim) window.Anim.onEnterPage(current);
      if(current===8){
        // 触发颁奖光束到勋章动画（A方案），B增强后由three-boost接管
        if(window.Anim) window.Anim.beamsToMedal();
        // 可在此尝试动态加载 three-boost.js（后续步骤实现）
      }
    }
  }

  // 初始化
  dots.forEach(btn=>{
    btn.addEventListener('click', ()=> goTo(parseInt(btn.dataset.target)));
  });
  pagesEl.addEventListener('scroll', throttle(updateDotsByScroll, 80));
  window.addEventListener('keydown', (e)=>{
    if(e.key==='ArrowDown' || e.key==='PageDown' || e.key==='ArrowRight') goTo(current+1);
    if(e.key==='ArrowUp' || e.key==='PageUp' || e.key==='ArrowLeft') goTo(current-1);
    if(e.key==='Home') goTo(1);
    if(e.key==='End') goTo(9);
  });

  // Page-3 星团弹窗
  const dialog = document.getElementById('cluster-dialog');
  const dialogText = document.getElementById('dialog-text');
  document.querySelectorAll('#page-3 .cluster').forEach(btn=>{
    btn.addEventListener('click', ()=>{
      const id = btn.dataset.id;
      const content = {
        '1': '遭遇“有问必答”的挑战题，考验了我们的数据处理能力。',
        '2': '与福建医科大学代表队初次接触，从他们认真的提问中感受到了赛事的价值。',
        '3': '一个技术插曲：用最简单的方式，实现了完美的PPT无痕播放。',
      }[id] || '';
      dialog.querySelector('#dialog-title').textContent = `星团${id}`;
      dialogText.textContent = content;
      dialog.hidden = false;
    });
  });
  dialog?.querySelector('.dialog-close')?.addEventListener('click', ()=> dialog.hidden = true);
  window.addEventListener('keydown', (e)=>{ if(e.key==='Escape') dialog.hidden = true; });

  // Page-4 陨石→规避→解决方案
  const solutionMap = {
    scorelog: '解决方案：规范化记录模板与复核流程，确保数据一致性。',
    music: '解决方案：建立曲库候选清单与AB试听机制，快速定调氛围。',
    timer: '解决方案：果断切换回纯软件方案，保障了终极PK环节的稳定性！',
  };
  document.querySelectorAll('#page-4 .asteroid').forEach(btn=>{
    btn.addEventListener('click', ()=>{
      const key = btn.dataset.key;
      const area = document.querySelector('#page-4 .evade-panel');
      const info = area.querySelector('.solution');
      info.textContent = solutionMap[key] || '';
      info.hidden = false;
      if(window.Anim) window.Anim.evadeAnimation(key);
    });
  });

  // Page-5 全息投影轮播
  let lineIndex = 0;
  const lines = Array.from(document.querySelectorAll('#page-5 .holo-line'));
  function showLine(i){
    lines.forEach((el,idx)=>{
      el.style.opacity = idx===i ? '1' : '0';
      el.style.transform = idx===i ? 'translateY(0)' : 'translateY(8px)';
    });
  }
  showLine(0);
  document.querySelector('#page-5 [data-action="prev"]').addEventListener('click', ()=>{ lineIndex=(lineIndex+lines.length-1)%lines.length; showLine(lineIndex); });
  document.querySelector('#page-5 [data-action="next"]').addEventListener('click', ()=>{ lineIndex=(lineIndex+1)%lines.length; showLine(lineIndex); });

  // Page-6 雷达→星云卡片
  const nebulaBtn = document.querySelector('#page-6 .nebula');
  const insight = document.getElementById('insight-card');
  nebulaBtn.addEventListener('click', ()=>{
    const expanded = nebulaBtn.getAttribute('aria-expanded')==='true';
    nebulaBtn.setAttribute('aria-expanded', String(!expanded));
    insight.hidden = expanded;
  });

  // 初始触发第一页动画
  if(window.Anim) window.Anim.onEnterPage(1);

  // 工具函数
  function throttle(fn, wait){
    let last = 0; let timer;
    return function(...args){
      const now = Date.now();
      if(now - last >= wait){ last = now; fn.apply(this, args); }
      else {
        clearTimeout(timer);
        timer = setTimeout(()=>{ last = Date.now(); fn.apply(this, args); }, wait - (now-last));
      }
    }
  }
})();

