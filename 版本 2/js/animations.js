/* 动画时间轴（A方案：GSAP优先，若无则CSS过渡） */
(function(){
  const hasGSAP = typeof window !== 'undefined' && !!window.gsap;
  function onEnterPage(pageId){
    if(!hasGSAP) return; // 无GSAP时用CSS初始状态
    const tl = gsap.timeline({ defaults: { ease: 'power2.out' }});
    switch(pageId){
      case 1:
        tl.from('#page-1 .title', { y: 20, opacity: 0, duration: 0.6 })
          .from('#page-1 .subtitle', { y: 16, opacity: 0, duration: 0.5 }, '-=0.3')
          .from('#page-1 .starship', { x: -200, opacity: 0, duration: 0.8 })
          .from('#page-1 .hyperspace-trail', { width: 0, duration: 0.8 }, '-=0.6')
          .from('#page-1 .blue-planet', { scale: 0.85, opacity: 0, duration: 0.8 }, '-=0.4')
          .from('#page-1 .fujian-glow', { opacity: 0, duration: 0.6 }, '-=0.4');
        break;
      case 2:
        tl.from('#page-2 .title', { y: 16, opacity: 0, duration: 0.6 })
          .from('#page-2 .crew-card', { y: 18, opacity: 0, duration: 0.5, stagger: 0.08 });
        break;
      case 3:
        tl.from('#page-3 .title', { y: 16, opacity: 0, duration: 0.6 })
          .from('#page-3 .starmap', { y: 20, opacity: 0, duration: 0.6 });
        break;
      case 4:
        tl.from('#page-4 .title', { y: 16, opacity: 0, duration: 0.6 })
          .from('#page-4 .asteroids-panel .asteroid', { y: 12, opacity: 0, duration: 0.4, stagger: 0.06 })
          .from('#page-4 .evade-panel', { opacity: 0, duration: 0.5 });
        break;
      case 5:
        tl.from('#page-5 .title', { y: 16, opacity: 0, duration: 0.6 })
          .from('#page-5 .holo-projection', { y: 12, opacity: 0, duration: 0.5 });
        break;
      case 6:
        tl.from('#page-6 .title', { y: 16, opacity: 0, duration: 0.6 })
          .from('#page-6 .radar', { scale: 0.9, opacity: 0, duration: 0.6 });
        break;
      case 7:
        tl.from('#page-7 .title', { y: 16, opacity: 0, duration: 0.6 })
          .from('#page-7 .bridge-screen', { y: 12, opacity: 0, duration: 0.5 });
        break;
      case 8:
        tl.from('#page-8 .title', { y: 16, opacity: 0, duration: 0.6 })
          .from('#page-8 .names-cloud span', { y: 10, opacity: 0, duration: 0.4, stagger: 0.04 });
        break;
      case 9:
        tl.from('#page-9 .title', { y: 16, opacity: 0, duration: 0.6 })
          .from('#page-9 .dashboard', { y: 12, opacity: 0, duration: 0.5 });
        break;
    }
  }

  function evadeAnimation(key){
    if(!hasGSAP) return;
    const ship = document.querySelector('.evade-ship');
    if(!ship) return;
    const dx = key === 'timer' ? 120 : key === 'music' ? -100 : 80;
    const dy = key === 'timer' ? -60 : key === 'music' ? 40 : -30;
    gsap.to(ship, { x: dx, y: dy, duration: 0.6, yoyo: true, repeat: 1, ease: 'power2.inOut' });
  }

  function beamsToMedal(){
    if(!hasGSAP) return;
    const tl = gsap.timeline();
    tl.to('.beam', { height: 160, duration: 0.6, stagger: 0.15, ease: 'power2.out' })
      .to('.medal', { opacity: 1, scale: 1, duration: 0.6, ease: 'back.out(1.4)' })
      .to('.honor-text', { opacity: 1, y: 0, duration: 0.5 }, '-=0.2');
  }

  window.Anim = {
    onEnterPage,
    evadeAnimation,
    beamsToMedal,
  };
})();

