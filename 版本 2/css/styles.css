/* 主题与基础布局 */
:root {
  --gold: #d4af37;
  --silver: #c0c0c0;
  --space: #0a0f1f;
  --blue: #3aa0ff;
  --text: #e6e6e6;
  --muted: #94a3b8;
  --glow: 0 0 16px rgba(212, 175, 55, 0.65), 0 0 32px rgba(212, 175, 55, 0.35);
}
* { box-sizing: border-box; }
html, body { height: 100%; background: var(--space); color: var(--text); margin: 0; font-family: system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, 'Helvetica Neue', 'Orbitron', sans-serif; }
.app { position: relative; overflow: hidden; }

/* 分屏与导航 */
.pages { height: 100dvh; overflow-y: auto; scroll-snap-type: y mandatory; }
.page { min-height: 100dvh; padding: 4rem 6vw; scroll-snap-align: start; position: relative; display: grid; place-items: center; }
.title { font-family: 'Orbitron', system-ui, sans-serif; letter-spacing: 0.5px; text-align: center; color: var(--silver); text-shadow: 0 0 8px rgba(255,255,255,.25); }
.subtitle { text-align: center; color: var(--muted); margin-top: .5rem; }

.pager { position: fixed; right: 1.2rem; top: 50%; transform: translateY(-50%); z-index: 20; }
.pager-dots { list-style: none; margin: 0; padding: 0; display: grid; gap: .6rem; }
.pager-dots button { width: .8rem; height: .8rem; border-radius: 50%; border: 1px solid var(--silver); background: transparent; cursor: pointer; }
.pager-dots button[aria-selected="true"], .pager-dots button:hover { background: var(--gold); box-shadow: var(--glow); }

/* 背景星空与装饰 */
.bg-stars::before, .bg-stars::after {
  content: ""; position: absolute; inset: 0; background: radial-gradient(circle at 20% 30%, rgba(255,255,255,.12), transparent 50%), radial-gradient(circle at 80% 70%, rgba(255,255,255,.08), transparent 40%), radial-gradient(circle at 60% 20%, rgba(58,160,255,.08), transparent 45%);
  filter: blur(0.8px); pointer-events: none;
}

/* Page 1 封面 */
.page-1 .scene { position: relative; width: 100%; max-width: 1200px; display: grid; place-items: center; gap: 1rem; }
.blue-planet { width: 240px; height: 240px; border-radius: 50%; background: radial-gradient(circle at 35% 35%, #66c1ff, #0c3a66 65%, #001425 100%); box-shadow: 0 0 24px rgba(58, 160, 255, 0.5); position: relative; }
.fujian-glow { position: absolute; inset: 20% 20% auto auto; width: 40%; height: 40%; border: 2px solid rgba(58,160,255,0.8); filter: drop-shadow(0 0 12px rgba(58,160,255,.8)); border-radius: 16px; opacity: .85; }
.starship { width: 140px; height: 60px; background: linear-gradient(90deg, var(--silver), #f8f8f8 60%, var(--silver)); border-radius: 40px; box-shadow: var(--glow); transform: skewX(-12deg); }
.hyperspace-trail { position: absolute; width: 60vw; height: 4px; background: linear-gradient(90deg, rgba(212,175,55,.0), rgba(212,175,55,.7), rgba(212,175,55,.0)); filter: blur(1px); }

/* Page 2 舰桥船员 */
.page-2 .crew-grid { display: grid; grid-template-columns: repeat(3, minmax(220px, 1fr)); gap: 1.2rem; width: min(100%, 1100px); }
.crew-card { position: relative; padding: 1rem; border: 1px solid rgba(192,192,192,.35); border-radius: 14px; background: rgba(255,255,255,0.04); backdrop-filter: blur(6px); cursor: default; transition: transform .25s ease, box-shadow .25s ease; }
.crew-card:focus, .crew-card:hover { transform: translateY(-4px); box-shadow: 0 6px 20px rgba(0,0,0,.4), 0 0 18px rgba(212,175,55,.25); }
.crew-card .holo { height: 120px; border-radius: 10px; background: linear-gradient(180deg, rgba(212,175,55,.15), rgba(255,255,255,.06)); border: 1px dashed rgba(212,175,55,.35); margin-bottom: .75rem; }
.crew-card .name { font-weight: 700; color: #fff; }
.crew-card .role { color: var(--gold); margin-top: .25rem; }
.crew-card .desc { position: absolute; inset: auto 1rem 1rem 1rem; background: rgba(10,15,31,.85); border: 1px solid rgba(212,175,55,.35); border-radius: 10px; padding: .75rem; transform: translateY(6px); opacity: 0; pointer-events: none; transition: all .25s ease; }
.crew-card:hover .desc, .crew-card:focus .desc { opacity: 1; transform: translateY(0); }

/* Page 3 航行日志 */
.page-3 .starmap { width: min(100%, 900px); height: 420px; position: relative; background: radial-gradient(circle at 50% 50%, rgba(24,46,92,.6), rgba(10,15,31,.4) 60%, rgba(10,15,31,.2)); border: 1px solid rgba(255,255,255,.08); border-radius: 16px; }
.cluster { position: absolute; width: 88px; height: 88px; border-radius: 999px; border: 1px solid rgba(212,175,55,.45); background: rgba(255,255,255,.04); color: #fff; cursor: pointer; box-shadow: 0 0 12px rgba(212,175,55,.25); }
.cluster[data-id="1"] { left: 8%; top: 18%; }
.cluster[data-id="2"] { left: 42%; top: 58%; }
.cluster[data-id="3"] { right: 10%; top: 25%; }
.dialog { position: fixed; inset: 0; display: grid; place-items: center; background: rgba(0,0,0,.45); backdrop-filter: blur(2px); }
.dialog[hidden] { display: none; }
.dialog-content { width: min(86vw, 560px); background: rgba(10,15,31,.9); border: 1px solid rgba(212,175,55,.35); border-radius: 14px; padding: 1rem 1.2rem; box-shadow: var(--glow); }
.dialog-close { margin-top: .8rem; }

/* Page 4 小行星带 */
.asteroids-panel { display: grid; gap: .8rem; }
.asteroid { padding: .6rem .9rem; border-radius: 999px; border: 1px solid rgba(255,0,0,.35); background: rgba(255,0,0,.12); color: #ffdede; cursor: pointer; box-shadow: 0 0 16px rgba(255,0,0,.25); }
.evade-panel { width: min(100%, 900px); height: 320px; position: relative; margin-top: 1rem; border: 1px solid rgba(255,255,255,.08); border-radius: 14px; background: rgba(255,255,255,.02); overflow: hidden; }
.evade-ship { position: absolute; left: 48%; top: 62%; width: 60px; height: 24px; background: linear-gradient(90deg, var(--silver), #fafafa 60%, var(--silver)); border-radius: 20px; box-shadow: var(--glow); transform: translate(-50%, -50%); }
.solution { position: absolute; right: 1rem; bottom: 1rem; background: rgba(10,15,31,.85); border: 1px solid rgba(212,175,55,.35); border-radius: 10px; padding: .6rem .8rem; }

/* Page 5 全息投影 */
.holo-projection { width: min(100%, 760px); border: 1px solid rgba(212,175,55,.35); border-radius: 14px; padding: 1rem; background: linear-gradient(180deg, rgba(255,255,255,.03), rgba(255,255,255,.01)); box-shadow: 0 0 24px rgba(212,175,55,.18) inset; }
.holo-line { opacity: 0; transform: translateY(8px); transition: all .35s ease; padding: .2rem 0; }
.holo-controls { display: flex; gap: .6rem; justify-content: flex-end; margin-top: .6rem; }

/* Page 6 雷达 */
.radar { position: relative; width: 360px; height: 360px; border-radius: 50%; background: radial-gradient(circle at 50% 50%, rgba(58,160,255,.15), rgba(10,15,31,.6)); border: 1px solid rgba(58,160,255,.45); box-shadow: 0 0 24px rgba(58,160,255,.18) inset; }
.radar-sweep { position: absolute; inset: 0; border-radius: 50%; background: conic-gradient(from 0deg, rgba(58,160,255,.25), rgba(58,160,255,0) 45%); filter: blur(1px); animation: sweep 3s linear infinite; }
@keyframes sweep { to { transform: rotate(360deg); } }
.nebula { position: absolute; left: 56%; top: 40%; transform: translate(-50%, -50%); padding: .5rem .8rem; border: 1px solid rgba(212,175,55,.35); background: rgba(255,255,255,.06); color: #fff; border-radius: 999px; box-shadow: 0 0 12px rgba(212,175,55,.25); }
.insight-card { width: min(100%, 640px); border: 1px solid rgba(212,175,55,.35); border-radius: 14px; padding: 1rem; background: rgba(10,15,31,.9); box-shadow: var(--glow); }

/* Page 7 铺垫 */
.bridge-screen { width: min(100%, 880px); height: 320px; border: 1px solid rgba(255,255,255,.08); border-radius: 14px; overflow: hidden; background: rgba(255,255,255,.02); }
.praises { list-style: none; margin: 0; padding: 1rem; display: grid; gap: .6rem; animation: scrollY 12s linear infinite; }
.praises:hover { animation-play-state: paused; }
@keyframes scrollY { 0% { transform: translateY(0); } 100% { transform: translateY(-50%); } }

/* Page 8 颁奖 */
.names-cloud { display: flex; gap: .8rem; flex-wrap: wrap; justify-content: center; max-width: 900px; }
.names-cloud span { padding: .35rem .6rem; border-radius: 999px; border: 1px solid rgba(255,255,255,.15); color: #fff; }
.names-cloud .target { color: var(--gold); border-color: rgba(212,175,55,.55); box-shadow: var(--glow); }
.beams { position: relative; width: 100%; height: 200px; margin-top: 1rem; }
.beam { position: absolute; width: 2px; height: 0; background: linear-gradient(180deg, rgba(212,175,55,1), rgba(212,175,55,0)); box-shadow: 0 0 14px rgba(212,175,55,.8); }
.beam-a { left: 20%; }
.beam-b { left: 50%; }
.beam-c { left: 80%; }
.medal { width: 120px; height: 120px; border-radius: 50%; border: 2px solid var(--gold); box-shadow: var(--glow); margin: 1rem auto; background: radial-gradient(circle at 35% 35%, rgba(255,255,255,.5), rgba(212,175,55,.65)); opacity: 0; transform: scale(.8); }
.honor-text { text-align: center; opacity: 0; transform: translateY(8px); }

/* Page 9 总结 */
.dashboard { display: grid; place-items: center; gap: 1rem; }
.gauge { position: relative; width: 240px; height: 240px; border-radius: 50%; border: 1px solid rgba(212,175,55,.35); box-shadow: 0 0 24px rgba(212,175,55,.18) inset; display: grid; place-items: center; }
.gauge-needle { position: absolute; width: 2px; height: 90px; background: var(--gold); transform-origin: bottom center; bottom: 50%; box-shadow: var(--glow); }
.gauge-value { margin-top: 260px; color: var(--silver); }
.dock-scene { position: relative; width: min(100%, 900px); height: 240px; border: 1px solid rgba(255,255,255,.08); border-radius: 14px; overflow: hidden; }
.dock-station { position: absolute; right: 6%; top: 30%; width: 180px; height: 120px; border: 1px solid rgba(255,255,255,.2); border-radius: 12px; }
.dock-ship { position: absolute; left: 8%; bottom: 12%; width: 80px; height: 28px; background: linear-gradient(90deg, var(--silver), #fafafa 60%, var(--silver)); border-radius: 16px; box-shadow: var(--glow); }

/* 可见性与通用 */
[hidden] { display: none !important; }
button { color: inherit; background: transparent; border: 1px solid rgba(255,255,255,.25); border-radius: 8px; padding: .4rem .6rem; cursor: pointer; }
button:hover { box-shadow: 0 0 12px rgba(255,255,255,.15); }

