<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>任务报告：“星光守护者”计划</title>
  <meta name="description" content="福建大学生安全知识竞赛 - 航行复盘" />
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <!-- 英文科技感字体（在线），无网将回退系统字体 -->
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="css/styles.css" />
</head>
<body>
  <div id="app" class="app" aria-label="任务报告：星光守护者计划">
    <nav class="pager" aria-label="页面导航">
      <ul id="pager-dots" class="pager-dots" role="tablist" aria-label="进度指示">
        <li><button data-target="1" role="tab" aria-controls="page-1" aria-label="封面"></button></li>
        <li><button data-target="2" role="tab" aria-controls="page-2" aria-label="舰桥船员"></button></li>
        <li><button data-target="3" role="tab" aria-controls="page-3" aria-label="航行日志"></button></li>
        <li><button data-target="4" role="tab" aria-controls="page-4" aria-label="小行星带"></button></li>
        <li><button data-target="5" role="tab" aria-controls="page-5" aria-label="来自过去的通讯"></button></li>
        <li><button data-target="6" role="tab" aria-controls="page-6" aria-label="啊哈时刻"></button></li>
        <li><button data-target="7" role="tab" aria-controls="page-7" aria-label="颁奖铺垫"></button></li>
        <li><button data-target="8" role="tab" aria-controls="page-8" aria-label="颁奖核心"></button></li>
        <li><button data-target="9" role="tab" aria-controls="page-9" aria-label="任务完成"></button></li>
      </ul>
    </nav>

    <main class="pages" tabindex="0">
      <!-- Page 1: 封面 -->
      <section id="page-1" class="page page-1" role="region" aria-labelledby="title-1">
        <div class="bg bg-stars" aria-hidden="true"></div>
        <div class="scene">
          <h1 id="title-1" class="title">任务报告：“星光守护者”计划</h1>
          <p class="subtitle">福建大学生安全知识竞赛 - 航行复盘</p>
          <div class="blue-planet" aria-hidden="true">
            <div class="fujian-glow" title="福建轮廓发光"></div>
          </div>
          <div class="starship" aria-label="守护者号星舰"></div>
          <div class="hyperspace-trail" aria-hidden="true"></div>
        </div>
      </section>

      <!-- Page 2: 舰桥船员 -->
      <section id="page-2" class="page page-2" role="region" aria-labelledby="title-2">
        <h2 id="title-2" class="title">“守护者号”核心船员</h2>
        <div class="crew-grid" role="list">
          <div class="crew-card" role="listitem" tabindex="0" data-name="赵明" data-role="舰长 (主持)">
            <div class="holo"></div>
            <div class="name">赵明</div>
            <div class="role">舰长 (主持)</div>
            <div class="desc" aria-hidden="true">角色描述：待提供</div>
          </div>
          <div class="crew-card" role="listitem" tabindex="0" data-name="马佳平" data-role="大副 (后台导演)">
            <div class="holo"></div>
            <div class="name">马佳平</div>
            <div class="role">大副 (后台导演)</div>
            <div class="desc" aria-hidden="true">角色描述：待提供</div>
          </div>
          <div class="crew-card" role="listitem" tabindex="0" data-name="蔡庆强" data-role="总工程师 (技术)">
            <div class="holo"></div>
            <div class="name">蔡庆强</div>
            <div class="role">总工程师 (技术)</div>
            <div class="desc" aria-hidden="true">角色描述：待提供</div>
          </div>
          <div class="crew-card" role="listitem" tabindex="0" data-name="陈柄宏" data-role="领航员 (VJ)">
            <div class="holo"></div>
            <div class="name">陈柄宏</div>
            <div class="role">领航员 (VJ)</div>
            <div class="desc" aria-hidden="true">角色描述：待提供</div>
          </div>
          <div class="crew-card" role="listitem" tabindex="0" data-name="梁金隆" data-role="通讯官 (DJ)">
            <div class="holo"></div>
            <div class="name">梁金隆</div>
            <div class="role">通讯官 (DJ)</div>
            <div class="desc" aria-hidden="true">角色描述：待提供</div>
          </div>
          <div class="crew-card" role="listitem" tabindex="0" data-name="盛捷" data-role="科学官 (计分)">
            <div class="holo"></div>
            <div class="name">盛捷</div>
            <div class="role">科学官 (计分)</div>
            <div class="desc" aria-hidden="true">角色描述：待提供</div>
          </div>
        </div>
      </section>

      <!-- Page 3: 航行日志 -->
      <section id="page-3" class="page page-3" role="region" aria-labelledby="title-3">
        <h2 id="title-3" class="title">航行日志：闪亮的星尘记忆</h2>
        <div class="starmap">
          <button class="cluster" data-id="1" aria-haspopup="dialog" aria-controls="cluster-dialog">星团1</button>
          <button class="cluster" data-id="2" aria-haspopup="dialog" aria-controls="cluster-dialog">星团2</button>
          <button class="cluster" data-id="3" aria-haspopup="dialog" aria-controls="cluster-dialog">星团3</button>
        </div>
        <div id="cluster-dialog" class="dialog" role="dialog" aria-modal="true" aria-labelledby="dialog-title" hidden>
          <div class="dialog-content">
            <h3 id="dialog-title">星团</h3>
            <p id="dialog-text"></p>
            <button class="dialog-close" aria-label="关闭">关闭</button>
          </div>
        </div>
      </section>

      <!-- Page 4: 小行星带 -->
      <section id="page-4" class="page page-4" role="region" aria-labelledby="title-4">
        <h2 id="title-4" class="title">警报！穿越小行星带！</h2>
        <div class="asteroids-panel" aria-label="挑战列表">
          <button class="asteroid red" data-key="scorelog">计分表记录难题</button>
          <button class="asteroid red" data-key="music">寻找合适的氛围音乐</button>
          <button class="asteroid red" data-key="timer">计时器接口接触不良</button>
        </div>
        <div class="evade-panel" aria-live="polite">
          <div class="evade-ship" aria-label="飞船"></div>
          <div class="evade-path" aria-hidden="true"></div>
          <div class="solution" hidden></div>
        </div>
      </section>

      <!-- Page 5: 来自过去的通讯 -->
      <section id="page-5" class="page page-5" role="region" aria-labelledby="title-5">
        <h2 id="title-5" class="title">捕获来自过去的通讯</h2>
        <div class="holo-projection" aria-live="polite">
          <div class="holo-line" data-index="0">不要紧张，这只是一次很小的比赛。</div>
          <div class="holo-line" data-index="1">做好份内事，学会拒绝。</div>
          <div class="holo-line" data-index="2">别给自己太大压力，人生有很多容错率。</div>
          <div class="holo-controls">
            <button data-action="prev" aria-label="上一条">◀</button>
            <button data-action="next" aria-label="下一条">▶</button>
          </div>
        </div>
      </section>

      <!-- Page 6: 啊哈时刻 -->
      <section id="page-6" class="page page-6" role="region" aria-labelledby="title-6">
        <h2 id="title-6" class="title">啊哈！发现“效率”星云！</h2>
        <div class="radar">
          <div class="radar-sweep"></div>
          <button class="nebula" aria-expanded="false" aria-controls="insight-card">效率星云</button>
        </div>
        <article id="insight-card" class="insight-card" hidden>
          <h3>工作方法的新发现</h3>
          <p>不必一味追求自动化和最新技术栈，在开发仅一人的情况下，优先满足核心需求才是王道。告别完美主义！</p>
        </article>
      </section>

      <!-- Page 7: 颁奖铺垫 -->
      <section id="page-7" class="page page-7" role="region" aria-labelledby="title-7">
        <h2 id="title-7" class="title">全舰广播：致敬我们的伙伴！</h2>
        <div class="bridge-screen">
          <ul class="praises" aria-live="polite">
            <li>为CTO送上小红花</li>
            <li>彩排时向云一直告诉我DJ的一些技巧</li>
            <li>为大梁送上小红花</li>
          </ul>
        </div>
      </section>

      <!-- Page 8: 颁奖核心 -->
      <section id="page-8" class="page page-8" role="region" aria-labelledby="title-8">
        <h2 id="title-8" class="title">本航程的“星光勋章”获得者是……</h2>
        <div class="names-cloud" aria-label="团队成员">
          <span>赵明</span><span>马佳平</span><span>蔡庆强</span><span>陈柄宏</span><span>梁金隆</span><span>盛捷</span><span class="target">向云</span>
        </div>
        <div class="beams">
          <div class="beam beam-a"></div>
          <div class="beam beam-b"></div>
          <div class="beam beam-c"></div>
        </div>
        <div class="medal" aria-hidden="true"></div>
        <div class="honor-text">
          <h3><strong>向云</strong> - 团队最闪亮的星！</h3>
          <p>感谢你，在关键时刻给予的专业指导与支持！</p>
        </div>
      </section>

      <!-- Page 9: 总结 -->
      <section id="page-9" class="page page-9" role="region" aria-labelledby="title-9">
        <h2 id="title-9" class="title">任务完成，成功返航！</h2>
        <div class="dashboard">
          <div class="gauge" role="img" aria-label="任务完成度：4.5 / 5">
            <div class="gauge-needle"></div>
            <div class="gauge-value">4.5 / 5</div>
          </div>
          <div class="dock-scene">
            <div class="dock-station"></div>
            <div class="dock-ship"></div>
          </div>
          <p class="closing">感谢每一位船员的付出，期待下一次起航！</p>
        </div>
      </section>
    </main>
  </div>

  <!-- GSAP CDN（可选），若不可用则使用CSS降级动画 -->
  <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js" defer></script>
  <script src="js/animations.js" defer></script>
  <script src="js/app.js" defer></script>
</body>
</html>

