// 船员数据模型
export interface CrewMember {
  id: string;
  name: string;
  position: string;
  role: string;
  description: string;
  avatar?: string;
}

// 记忆数据模型
export interface Memory {
  id: string;
  title: string;
  content: string;
  position: { x: number; y: number };
}

// 挑战数据模型
export interface Challenge {
  id: string;
  name: string;
  solution: string;
  position: { x: number; y: number };
}

// 智慧建议数据模型
export interface WisdomAdvice {
  id: string;
  content: string;
  delay: number; // 播放延迟时间（毫秒）
}

// 赞美数据模型
export interface Praise {
  id: string;
  content: string;
}

// 全局应用状态
export interface AppState {
  currentPageIndex: number;
  isAnimationPlaying: boolean;
  isSoundEnabled: boolean;
  userProgress: number;
}

// 状态管理Action类型
export type AppAction =
  | { type: 'SET_PAGE'; payload: number }
  | { type: 'SET_ANIMATION_PLAYING'; payload: boolean }
  | { type: 'TOGGLE_SOUND' }
  | { type: 'UPDATE_PROGRESS'; payload: number };

// 页面路由类型
export interface PageRoute {
  path: string;
  name: string;
  component: React.ComponentType;
}

// 动画配置类型
export interface AnimationConfig {
  duration: number;
  delay?: number;
  ease?: string;
  repeat?: number;
}

// 粒子效果配置
export interface ParticleConfig {
  count: number;
  size: { min: number; max: number };
  speed: { min: number; max: number };
  color: string;
  opacity: { min: number; max: number };
}

// 光束效果配置
export interface LightBeamConfig {
  startPosition: { x: number; y: number };
  endPosition: { x: number; y: number };
  color: string;
  width: number;
  duration: number;
}

// 雷达扫描配置
export interface RadarConfig {
  radius: number;
  sweepAngle: number;
  sweepDuration: number;
  gridLines: number;
}

// 仪表盘数据
export interface DashboardData {
  value: number;
  maxValue: number;
  label: string;
  unit?: string;
}