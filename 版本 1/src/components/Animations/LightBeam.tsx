import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface LightBeamProps {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  color?: string;
  width?: number;
  duration?: number;
  delay?: number;
  intensity?: number;
  animated?: boolean;
  className?: string;
}

export function LightBeam({
  startX,
  startY,
  endX,
  endY,
  color = '#fbbf24',
  width = 4,
  duration = 1,
  delay = 0,
  intensity = 1,
  animated = true,
  className
}: LightBeamProps) {
  const length = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
  const angle = Math.atan2(endY - startY, endX - startX) * 180 / Math.PI;
  
  return (
    <motion.div
      className={cn('absolute pointer-events-none', className)}
      style={{
        left: startX,
        top: startY,
        width: length,
        height: width,
        transformOrigin: '0 50%',
        transform: `rotate(${angle}deg)`
      }}
      initial={animated ? { scaleX: 0, opacity: 0 } : {}}
      animate={animated ? { scaleX: 1, opacity: intensity } : { opacity: intensity }}
      transition={{
        duration,
        delay,
        ease: 'easeOut'
      }}
    >
      {/* 主光束 */}
      <div
        className="w-full h-full relative"
        style={{
          background: `linear-gradient(90deg, transparent, ${color}, transparent)`,
          filter: 'blur(1px)'
        }}
      >
        {/* 内核光束 */}
        <div
          className="absolute inset-0"
          style={{
            background: `linear-gradient(90deg, transparent, ${color}, transparent)`,
            filter: 'blur(0.5px)',
            opacity: 0.8
          }}
        />
        
        {/* 外层光晕 */}
        <div
          className="absolute inset-0 transform scale-y-150"
          style={{
            background: `linear-gradient(90deg, transparent, ${color}40, transparent)`,
            filter: 'blur(3px)'
          }}
        />
        
        {/* 粒子效果 */}
        {animated && (
          <motion.div
            className="absolute inset-0 overflow-hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: delay + duration * 0.5 }}
          >
            {Array.from({ length: 8 }, (_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 rounded-full"
                style={{
                  backgroundColor: color,
                  left: `${10 + i * 10}%`,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  boxShadow: `0 0 4px ${color}`
                }}
                animate={{
                  scale: [0, 1.5, 0],
                  opacity: [0, 1, 0]
                }}
                transition={{
                  duration: 0.8,
                  delay: i * 0.1,
                  repeat: Infinity,
                  repeatDelay: 2
                }}
              />
            ))}
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}

// 多光束聚焦效果
interface ConvergingBeamsProps {
  centerX: number;
  centerY: number;
  beams?: Array<{
    startX: number;
    startY: number;
    color?: string;
    delay?: number;
  }>;
  onComplete?: () => void;
  className?: string;
}

export function ConvergingBeams({
  centerX,
  centerY,
  beams = [],
  onComplete,
  className
}: ConvergingBeamsProps) {
  const defaultBeams = [
    { startX: centerX - 200, startY: centerY - 100, color: '#fbbf24', delay: 0 },
    { startX: centerX + 200, startY: centerY - 100, color: '#f59e0b', delay: 0.2 },
    { startX: centerX, startY: centerY - 200, color: '#d97706', delay: 0.4 }
  ];
  
  const finalBeams = beams.length > 0 ? beams : defaultBeams;
  
  React.useEffect(() => {
    if (onComplete) {
      const maxDelay = Math.max(...finalBeams.map(beam => beam.delay || 0));
      const timer = setTimeout(onComplete, (maxDelay + 1.5) * 1000);
      return () => clearTimeout(timer);
    }
  }, [finalBeams, onComplete]);
  
  return (
    <div className={cn('absolute inset-0 pointer-events-none', className)}>
      {finalBeams.map((beam, index) => (
        <LightBeam
          key={index}
          startX={beam.startX}
          startY={beam.startY}
          endX={centerX}
          endY={centerY}
          color={beam.color}
          delay={beam.delay}
          duration={1.5}
          intensity={0.9}
        />
      ))}
      
      {/* 中心聚焦点 */}
      <motion.div
        className="absolute w-8 h-8 rounded-full"
        style={{
          left: centerX - 16,
          top: centerY - 16,
          backgroundColor: '#fbbf24',
          boxShadow: '0 0 20px #fbbf24, 0 0 40px #fbbf24, 0 0 60px #fbbf24'
        }}
        initial={{ scale: 0, opacity: 0 }}
        animate={{ 
          scale: [0, 1.5, 1],
          opacity: [0, 1, 1]
        }}
        transition={{
          delay: Math.max(...finalBeams.map(beam => beam.delay || 0)) + 1,
          duration: 0.8
        }}
      />
      
      {/* 聚焦爆发效果 */}
      <motion.div
        className="absolute rounded-full border-4 border-yellow-400"
        style={{
          left: centerX - 20,
          top: centerY - 20,
          width: 40,
          height: 40
        }}
        initial={{ scale: 0, opacity: 0 }}
        animate={{
          scale: [0, 3, 5],
          opacity: [0, 0.8, 0]
        }}
        transition={{
          delay: Math.max(...finalBeams.map(beam => beam.delay || 0)) + 1.5,
          duration: 1
        }}
      />
    </div>
  );
}

// 扫描光束效果
interface ScanBeamProps {
  width: number;
  height: number;
  direction?: 'horizontal' | 'vertical';
  color?: string;
  speed?: number;
  thickness?: number;
  className?: string;
}

export function ScanBeam({
  width,
  height,
  direction = 'horizontal',
  color = '#22c55e',
  speed = 2,
  thickness = 2,
  className
}: ScanBeamProps) {
  return (
    <div 
      className={cn('absolute overflow-hidden pointer-events-none', className)}
      style={{ width, height }}
    >
      <motion.div
        className="absolute"
        style={{
          width: direction === 'horizontal' ? width : thickness,
          height: direction === 'horizontal' ? thickness : height,
          background: `linear-gradient(${direction === 'horizontal' ? '90deg' : '0deg'}, transparent, ${color}, transparent)`,
          filter: 'blur(1px)',
          boxShadow: `0 0 10px ${color}`
        }}
        animate={{
          [direction === 'horizontal' ? 'y' : 'x']: [
            direction === 'horizontal' ? -thickness : -thickness,
            direction === 'horizontal' ? height + thickness : width + thickness
          ]
        }}
        transition={{
          duration: speed,
          repeat: Infinity,
          ease: 'linear'
        }}
      />
    </div>
  );
}

// 脉冲光束效果
interface PulseBeamProps {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  color?: string;
  pulseSpeed?: number;
  className?: string;
}

export function PulseBeam({
  startX,
  startY,
  endX,
  endY,
  color = '#3b82f6',
  pulseSpeed = 1,
  className
}: PulseBeamProps) {
  const length = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
  const angle = Math.atan2(endY - startY, endX - startX) * 180 / Math.PI;
  
  return (
    <div
      className={cn('absolute pointer-events-none', className)}
      style={{
        left: startX,
        top: startY,
        width: length,
        height: 4,
        transformOrigin: '0 50%',
        transform: `rotate(${angle}deg)`
      }}
    >
      {/* 基础光束 */}
      <div
        className="w-full h-full"
        style={{
          background: `linear-gradient(90deg, transparent, ${color}60, transparent)`,
          filter: 'blur(1px)'
        }}
      />
      
      {/* 脉冲效果 */}
      <motion.div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(90deg, ${color}, transparent)`,
          filter: 'blur(2px)'
        }}
        animate={{
          x: [0, length],
          opacity: [1, 0]
        }}
        transition={{
          duration: pulseSpeed,
          repeat: Infinity,
          ease: 'easeOut'
        }}
      />
    </div>
  );
}

// 激光网格效果
interface LaserGridProps {
  width: number;
  height: number;
  gridSize?: number;
  color?: string;
  animated?: boolean;
  className?: string;
}

export function LaserGrid({
  width,
  height,
  gridSize = 50,
  color = '#06b6d4',
  animated = true,
  className
}: LaserGridProps) {
  const horizontalLines = Math.floor(height / gridSize) + 1;
  const verticalLines = Math.floor(width / gridSize) + 1;
  
  return (
    <div 
      className={cn('absolute pointer-events-none', className)}
      style={{ width, height }}
    >
      {/* 水平线 */}
      {Array.from({ length: horizontalLines }, (_, i) => (
        <motion.div
          key={`h-${i}`}
          className="absolute w-full h-0.5"
          style={{
            top: i * gridSize,
            background: `linear-gradient(90deg, transparent, ${color}60, transparent)`,
            filter: 'blur(0.5px)'
          }}
          initial={animated ? { scaleX: 0 } : {}}
          animate={animated ? { scaleX: 1 } : {}}
          transition={{
            delay: i * 0.1,
            duration: 0.8
          }}
        />
      ))}
      
      {/* 垂直线 */}
      {Array.from({ length: verticalLines }, (_, i) => (
        <motion.div
          key={`v-${i}`}
          className="absolute w-0.5 h-full"
          style={{
            left: i * gridSize,
            background: `linear-gradient(0deg, transparent, ${color}60, transparent)`,
            filter: 'blur(0.5px)'
          }}
          initial={animated ? { scaleY: 0 } : {}}
          animate={animated ? { scaleY: 1 } : {}}
          transition={{
            delay: i * 0.1 + 0.5,
            duration: 0.8
          }}
        />
      ))}
      
      {/* 交点发光效果 */}
      {animated && Array.from({ length: horizontalLines }, (_, i) => 
        Array.from({ length: verticalLines }, (_, j) => (
          <motion.div
            key={`point-${i}-${j}`}
            className="absolute w-1 h-1 rounded-full"
            style={{
              left: j * gridSize - 2,
              top: i * gridSize - 2,
              backgroundColor: color,
              boxShadow: `0 0 4px ${color}`
            }}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ 
              scale: [0, 1.5, 1],
              opacity: [0, 1, 0.7]
            }}
            transition={{
              delay: (i + j) * 0.05 + 1,
              duration: 0.5
            }}
          />
        ))
      )}
    </div>
  );
}