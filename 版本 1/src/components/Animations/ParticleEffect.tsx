import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface Particle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  opacity: number;
  color: string;
  life: number;
  maxLife: number;
}

interface ParticleEffectProps {
  count?: number;
  colors?: string[];
  speed?: number;
  size?: [number, number];
  opacity?: [number, number];
  life?: [number, number];
  direction?: 'up' | 'down' | 'left' | 'right' | 'random';
  className?: string;
  width?: number;
  height?: number;
}

export function ParticleEffect({
  count = 50,
  colors = ['#3b82f6', '#8b5cf6', '#06b6d4', '#10b981'],
  speed = 1,
  size = [1, 3],
  opacity = [0.3, 0.8],
  life = [3000, 6000],
  direction = 'up',
  className,
  width = 800,
  height = 600
}: ParticleEffectProps) {
  const [particles, setParticles] = React.useState<Particle[]>([]);
  const animationRef = React.useRef<number>();
  const lastTimeRef = React.useRef<number>(0);
  
  // 使用 useMemo 缓存 colors 数组，避免每次渲染都重新创建
  const memoizedColors = React.useMemo(() => colors, [JSON.stringify(colors)]);
  const memoizedSize = React.useMemo(() => size, [JSON.stringify(size)]);
  const memoizedOpacity = React.useMemo(() => opacity, [JSON.stringify(opacity)]);
  const memoizedLife = React.useMemo(() => life, [JSON.stringify(life)]);
  
  React.useEffect(() => {
    const createParticle = (id: number): Particle => {
      const particleSize = memoizedSize[0] + Math.random() * (memoizedSize[1] - memoizedSize[0]);
      const particleOpacity = memoizedOpacity[0] + Math.random() * (memoizedOpacity[1] - memoizedOpacity[0]);
      const particleLife = memoizedLife[0] + Math.random() * (memoizedLife[1] - memoizedLife[0]);
      const color = memoizedColors[Math.floor(Math.random() * memoizedColors.length)];
      
      let x, y, vx, vy;
      
      switch (direction) {
        case 'up':
          x = Math.random() * width;
          y = height + 10;
          vx = (Math.random() - 0.5) * speed * 0.5;
          vy = -Math.random() * speed - 0.5;
          break;
        case 'down':
          x = Math.random() * width;
          y = -10;
          vx = (Math.random() - 0.5) * speed * 0.5;
          vy = Math.random() * speed + 0.5;
          break;
        case 'left':
          x = width + 10;
          y = Math.random() * height;
          vx = -Math.random() * speed - 0.5;
          vy = (Math.random() - 0.5) * speed * 0.5;
          break;
        case 'right':
          x = -10;
          y = Math.random() * height;
          vx = Math.random() * speed + 0.5;
          vy = (Math.random() - 0.5) * speed * 0.5;
          break;
        default: // random
          x = Math.random() * width;
          y = Math.random() * height;
          vx = (Math.random() - 0.5) * speed;
          vy = (Math.random() - 0.5) * speed;
      }
      
      return {
        id,
        x,
        y,
        vx,
        vy,
        size: particleSize,
        opacity: particleOpacity,
        color,
        life: particleLife,
        maxLife: particleLife
      };
    };
    
    // 初始化粒子
    const initialParticles = Array.from({ length: count }, (_, i) => createParticle(i));
    setParticles(initialParticles);
    
    let particleId = count;
    
    const animate = (currentTime: number) => {
      const deltaTime = currentTime - lastTimeRef.current;
      lastTimeRef.current = currentTime;
      
      setParticles(prevParticles => {
        const updatedParticles = prevParticles
          .map(particle => {
            const newLife = particle.life - deltaTime;
            if (newLife <= 0) {
              return createParticle(particleId++);
            }
            
            return {
              ...particle,
              x: particle.x + particle.vx,
              y: particle.y + particle.vy,
              life: newLife,
              opacity: particle.opacity * (newLife / particle.maxLife)
            };
          })
          .filter(particle => {
            return particle.x >= -50 && particle.x <= width + 50 &&
                   particle.y >= -50 && particle.y <= height + 50;
          });
        
        // 确保粒子数量
        while (updatedParticles.length < count) {
          updatedParticles.push(createParticle(particleId++));
        }
        
        return updatedParticles;
      });
      
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animationRef.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [count, memoizedColors, memoizedSize, memoizedOpacity, memoizedLife, direction, height, speed, width]);
  
  return (
    <div 
      className={cn('absolute inset-0 overflow-hidden pointer-events-none', className)}
      style={{ width, height }}
    >
      {particles.map(particle => (
        <div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            left: particle.x,
            top: particle.y,
            width: particle.size,
            height: particle.size,
            backgroundColor: particle.color,
            opacity: particle.opacity,
            boxShadow: `0 0 ${particle.size * 2}px ${particle.color}`,
            transform: 'translate(-50%, -50%)'
          }}
        />
      ))}
    </div>
  );
}

// 星尘粒子效果
interface StardustProps {
  count?: number;
  className?: string;
}

export function Stardust({ count = 30, className }: StardustProps) {
  return (
    <ParticleEffect
      count={count}
      colors={['#fbbf24', '#f59e0b', '#d97706', '#92400e']}
      speed={0.5}
      size={[1, 2]}
      opacity={[0.4, 0.9]}
      life={[4000, 8000]}
      direction="random"
      className={className}
    />
  );
}

// 能量粒子效果
interface EnergyParticlesProps {
  count?: number;
  className?: string;
}

export function EnergyParticles({ count = 40, className }: EnergyParticlesProps) {
  return (
    <ParticleEffect
      count={count}
      colors={['#3b82f6', '#1d4ed8', '#1e40af', '#06b6d4']}
      speed={1.5}
      size={[2, 4]}
      opacity={[0.3, 0.7]}
      life={[2000, 4000]}
      direction="up"
      className={className}
    />
  );
}

// 魔法粒子效果
interface MagicParticlesProps {
  count?: number;
  className?: string;
}

export function MagicParticles({ count = 25, className }: MagicParticlesProps) {
  return (
    <ParticleEffect
      count={count}
      colors={['#8b5cf6', '#7c3aed', '#6d28d9', '#a855f7']}
      speed={0.8}
      size={[1, 3]}
      opacity={[0.5, 0.8]}
      life={[3000, 6000]}
      direction="random"
      className={className}
    />
  );
}

// 简单的浮动粒子组件
interface FloatingParticleProps {
  x: number;
  y: number;
  size?: number;
  color?: string;
  duration?: number;
  delay?: number;
  className?: string;
}

export function FloatingParticle({
  x,
  y,
  size = 4,
  color = '#3b82f6',
  duration = 3,
  delay = 0,
  className
}: FloatingParticleProps) {
  return (
    <motion.div
      className={cn('absolute rounded-full pointer-events-none', className)}
      style={{
        left: x,
        top: y,
        width: size,
        height: size,
        backgroundColor: color,
        boxShadow: `0 0 ${size * 2}px ${color}`
      }}
      initial={{
        opacity: 0,
        scale: 0,
        y: 0
      }}
      animate={{
        opacity: [0, 1, 1, 0],
        scale: [0, 1, 1.2, 0],
        y: [-20, -40, -60, -80]
      }}
      transition={{
        duration,
        delay,
        ease: 'easeOut'
      }}
    />
  );
}

// 爆炸粒子效果
interface ExplosionParticlesProps {
  x: number;
  y: number;
  count?: number;
  colors?: string[];
  onComplete?: () => void;
  className?: string;
}

export function ExplosionParticles({
  x,
  y,
  count = 12,
  colors = ['#fbbf24', '#f59e0b', '#ef4444', '#dc2626'],
  onComplete,
  className
}: ExplosionParticlesProps) {
  const particles = React.useMemo(() => {
    return Array.from({ length: count }, (_, i) => {
      const angle = (i / count) * Math.PI * 2;
      const velocity = 50 + Math.random() * 30;
      const size = 2 + Math.random() * 3;
      const color = colors[Math.floor(Math.random() * colors.length)];
      
      return {
        id: i,
        angle,
        velocity,
        size,
        color,
        vx: Math.cos(angle) * velocity,
        vy: Math.sin(angle) * velocity
      };
    });
  }, [count, colors]);
  
  React.useEffect(() => {
    if (onComplete) {
      const timer = setTimeout(onComplete, 1000);
      return () => clearTimeout(timer);
    }
  }, [onComplete]);
  
  return (
    <div className={cn('absolute pointer-events-none', className)}>
      {particles.map(particle => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            left: x,
            top: y,
            width: particle.size,
            height: particle.size,
            backgroundColor: particle.color,
            boxShadow: `0 0 ${particle.size * 2}px ${particle.color}`
          }}
          initial={{
            x: 0,
            y: 0,
            opacity: 1,
            scale: 1
          }}
          animate={{
            x: particle.vx,
            y: particle.vy,
            opacity: 0,
            scale: 0
          }}
          transition={{
            duration: 1,
            ease: 'easeOut'
          }}
        />
      ))}
    </div>
  );
}