import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface RadarScanProps {
  size?: number;
  onTargetFound?: () => void;
  targets?: Array<{ x: number; y: number; label: string }>;
  scanning?: boolean;
  className?: string;
}

export function RadarScan({
  size = 300,
  onTargetFound,
  targets = [],
  scanning = true,
  className
}: RadarScanProps) {
  const [foundTargets, setFoundTargets] = React.useState<number[]>([]);
  const [scanAngle, setScanAngle] = React.useState(0);
  
  React.useEffect(() => {
    if (!scanning) return;
    
    const interval = setInterval(() => {
      setScanAngle(prev => (prev + 2) % 360);
    }, 50);
    
    return () => clearInterval(interval);
  }, [scanning]);
  
  React.useEffect(() => {
    if (!scanning) return;
    
    targets.forEach((target, index) => {
      const targetAngle = Math.atan2(target.y - 50, target.x - 50) * 180 / Math.PI;
      const normalizedTargetAngle = (targetAngle + 360) % 360;
      const normalizedScanAngle = (scanAngle + 360) % 360;
      
      const angleDiff = Math.abs(normalizedScanAngle - normalizedTargetAngle);
      const minDiff = Math.min(angleDiff, 360 - angleDiff);
      
      if (minDiff < 5 && !foundTargets.includes(index)) {
        setFoundTargets(prev => [...prev, index]);
        if (onTargetFound) {
          setTimeout(() => onTargetFound(), 500);
        }
      }
    });
  }, [scanAngle, targets, foundTargets, onTargetFound, scanning]);
  
  return (
    <div 
      className={cn('relative', className)}
      style={{ width: size, height: size }}
    >
      {/* 雷达背景 */}
      <div className="absolute inset-0 rounded-full border-2 border-green-400/30 bg-black/50">
        {/* 同心圆 */}
        {[1, 2, 3, 4].map(ring => (
          <div
            key={ring}
            className="absolute border border-green-400/20 rounded-full"
            style={{
              width: `${ring * 25}%`,
              height: `${ring * 25}%`,
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)'
            }}
          />
        ))}
        
        {/* 十字线 */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-full h-0.5 bg-green-400/20" />
        </div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="h-full w-0.5 bg-green-400/20" />
        </div>
        
        {/* 扫描线 */}
        {scanning && (
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            style={{ transform: `rotate(${scanAngle}deg)` }}
          >
            <div
              className="absolute w-1/2 h-0.5 origin-left"
              style={{
                background: 'linear-gradient(90deg, #22c55e, transparent)',
                filter: 'blur(1px)'
              }}
            />
            <div
              className="absolute w-1/2 h-1 origin-left opacity-60"
              style={{
                background: 'linear-gradient(90deg, #22c55e80, transparent)',
                filter: 'blur(3px)'
              }}
            />
          </motion.div>
        )}
        
        {/* 扫描区域高亮 */}
        {scanning && (
          <motion.div
            className="absolute inset-0"
            style={{ transform: `rotate(${scanAngle}deg)` }}
          >
            <div
              className="absolute w-1/2 h-full origin-left opacity-10"
              style={{
                background: 'conic-gradient(from 0deg, #22c55e, transparent 30deg)',
                clipPath: 'polygon(0 50%, 50% 0, 50% 100%)'
              }}
            />
          </motion.div>
        )}
        
        {/* 目标点 */}
        {targets.map((target, index) => (
          <motion.div
            key={index}
            className="absolute w-3 h-3 transform -translate-x-1/2 -translate-y-1/2"
            style={{
              left: `${target.x}%`,
              top: `${target.y}%`
            }}
            initial={{ scale: 0, opacity: 0 }}
            animate={foundTargets.includes(index) ? {
              scale: [0, 1.5, 1],
              opacity: [0, 1, 1]
            } : {}}
            transition={{ duration: 0.5 }}
          >
            {foundTargets.includes(index) && (
              <>
                {/* 目标标记 */}
                <motion.div
                  className="w-full h-full rounded-full bg-red-400 border-2 border-red-300"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [1, 0.7, 1]
                  }}
                  transition={{
                    duration: 1,
                    repeat: Infinity
                  }}
                />
                
                {/* 目标标签 */}
                <motion.div
                  className="absolute left-full top-0 ml-2 px-2 py-1 bg-black/80 text-green-400 text-xs rounded border border-green-400/50 whitespace-nowrap"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  {target.label}
                </motion.div>
                
                {/* 脉冲效果 */}
                <motion.div
                  className="absolute inset-0 rounded-full border-2 border-red-400"
                  animate={{
                    scale: [1, 3],
                    opacity: [0.8, 0]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity
                  }}
                />
              </>
            )}
          </motion.div>
        ))}
        
        {/* 中心点 */}
        <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <motion.div
            className="w-2 h-2 rounded-full bg-green-400"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [1, 0.7, 1]
            }}
            transition={{
              duration: 1,
              repeat: Infinity
            }}
          />
        </div>
      </div>
      
      {/* 雷达屏幕效果 */}
      <div className="absolute inset-0 rounded-full overflow-hidden pointer-events-none">
        {/* 扫描线残影 */}
        {scanning && (
          <motion.div
            className="absolute inset-0"
            style={{ transform: `rotate(${scanAngle - 30}deg)` }}
          >
            <div
              className="absolute w-1/2 h-0.5 origin-left opacity-30"
              style={{
                background: 'linear-gradient(90deg, #22c55e, transparent)'
              }}
            />
          </motion.div>
        )}
        
        {/* 屏幕闪烁效果 */}
        <motion.div
          className="absolute inset-0 bg-green-400/5"
          animate={{
            opacity: [0, 0.1, 0]
          }}
          transition={{
            duration: 0.1,
            repeat: Infinity,
            repeatDelay: Math.random() * 3
          }}
        />
      </div>
      
      {/* 雷达信息显示 */}
      <div className="absolute -bottom-8 left-0 right-0 text-center">
        <div className="text-green-400 text-sm font-mono">
          {scanning ? '扫描中...' : '扫描完成'}
        </div>
        <div className="text-green-400/70 text-xs font-mono mt-1">
          目标: {foundTargets.length}/{targets.length}
        </div>
      </div>
    </div>
  );
}

// 简化版雷达组件
interface SimpleRadarProps {
  size?: number;
  active?: boolean;
  className?: string;
}

export function SimpleRadar({ size = 100, active = true, className }: SimpleRadarProps) {
  return (
    <div 
      className={cn('relative', className)}
      style={{ width: size, height: size }}
    >
      <div className="absolute inset-0 rounded-full border-2 border-green-400/50 bg-black/30">
        {/* 同心圆 */}
        {[1, 2].map(ring => (
          <div
            key={ring}
            className="absolute border border-green-400/30 rounded-full"
            style={{
              width: `${ring * 50}%`,
              height: `${ring * 50}%`,
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)'
            }}
          />
        ))}
        
        {/* 扫描线 */}
        {active && (
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            animate={{ rotate: 360 }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'linear'
            }}
          >
            <div
              className="absolute w-1/2 h-0.5 origin-left"
              style={{
                background: 'linear-gradient(90deg, #22c55e, transparent)'
              }}
            />
          </motion.div>
        )}
        
        {/* 中心点 */}
        <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="w-1 h-1 rounded-full bg-green-400" />
        </div>
      </div>
    </div>
  );
}