import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface StarshipJumpProps {
  onAnimationComplete?: () => void;
  className?: string;
}

export function StarshipJump({ onAnimationComplete, className }: StarshipJumpProps) {
  return (
    <div className={cn('relative w-full h-full overflow-hidden', className)}>
      {/* 超空间隧道背景 */}
      <motion.div
        className="absolute inset-0"
        initial={{ opacity: 1 }}
        animate={{ opacity: 0 }}
        transition={{ duration: 2, delay: 1 }}
      >
        {/* 隧道线条 */}
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute h-0.5 bg-gradient-to-r from-transparent via-blue-400 to-transparent"
            style={{
              left: '50%',
              top: `${45 + (i - 10) * 2}%`,
              width: '2px',
              transformOrigin: 'left center'
            }}
            initial={{
              scaleX: 0,
              x: '-50%'
            }}
            animate={{
              scaleX: [0, 100, 0],
              opacity: [0, 1, 0]
            }}
            transition={{
              duration: 1.5,
              delay: i * 0.05,
              ease: 'easeOut'
            }}
          />
        ))}
        
        {/* 粒子效果 */}
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={`particle-${i}`}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
            initial={{ scale: 0, opacity: 0 }}
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 1, 0],
              x: [0, (Math.random() - 0.5) * 200],
              y: [0, (Math.random() - 0.5) * 200]
            }}
            transition={{
              duration: 2,
              delay: Math.random() * 1,
              ease: 'easeOut'
            }}
          />
        ))}
      </motion.div>
      
      {/* 星舰主体 */}
      <motion.div
        className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"
        initial={{
          scale: 0.1,
          opacity: 0,
          rotateY: 45,
          z: -1000
        }}
        animate={{
          scale: [0.1, 0.8, 1],
          opacity: [0, 0.8, 1],
          rotateY: [45, 10, 0],
          z: [-1000, -100, 0]
        }}
        transition={{
          duration: 2.5,
          ease: 'easeOut',
          times: [0, 0.7, 1]
        }}
        onAnimationComplete={onAnimationComplete}
      >
        {/* 星舰SVG */}
        <svg
          width="200"
          height="120"
          viewBox="0 0 200 120"
          className="filter drop-shadow-lg"
        >
          {/* 主船体 */}
          <motion.path
            d="M20 60 L160 40 L180 60 L160 80 Z"
            fill="url(#shipGradient)"
            stroke="#ffd700"
            strokeWidth="2"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 1.5, delay: 1 }}
          />
          
          {/* 引擎舱 */}
          <motion.circle
            cx="25"
            cy="60"
            r="15"
            fill="url(#engineGradient)"
            stroke="#00bfff"
            strokeWidth="2"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          />
          
          {/* 舰桥 */}
          <motion.rect
            x="140"
            y="50"
            width="30"
            height="20"
            rx="5"
            fill="url(#bridgeGradient)"
            stroke="#ffd700"
            strokeWidth="1"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 1.8 }}
          />
          
          {/* 渐变定义 */}
          <defs>
            <linearGradient id="shipGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#1e3a8a" />
              <stop offset="50%" stopColor="#3b82f6" />
              <stop offset="100%" stopColor="#1e40af" />
            </linearGradient>
            
            <radialGradient id="engineGradient" cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor="#00bfff" />
              <stop offset="100%" stopColor="#0066cc" />
            </radialGradient>
            
            <linearGradient id="bridgeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#ffd700" />
              <stop offset="100%" stopColor="#ffb700" />
            </linearGradient>
          </defs>
        </svg>
        
        {/* 引擎光效 */}
        <motion.div
          className="absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: [0, 1, 0.7] }}
          transition={{ duration: 1, delay: 1.5 }}
        >
          <motion.div
            className="w-full h-full rounded-full bg-blue-400"
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.8, 0.4, 0.8]
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
            style={{
              boxShadow: '0 0 20px #00bfff, 0 0 40px #00bfff80'
            }}
          />
        </motion.div>
      </motion.div>
      
      {/* 冲击波效果 */}
      <motion.div
        className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: [0, 3, 5], opacity: [0, 0.6, 0] }}
        transition={{ duration: 1.5, delay: 0.5 }}
      >
        <div
          className="w-32 h-32 rounded-full border-2 border-blue-400"
          style={{
            boxShadow: '0 0 50px #00bfff80'
          }}
        />
      </motion.div>
      
      {/* 空间扭曲效果 */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: [0, 0.3, 0] }}
        transition={{ duration: 2, delay: 0.2 }}
      >
        <div
          className="w-full h-full"
          style={{
            background: 'radial-gradient(ellipse at center, transparent 30%, rgba(0, 191, 255, 0.1) 50%, transparent 70%)',
            filter: 'blur(20px)'
          }}
        />
      </motion.div>
    </div>
  );
}

// 简化版星舰组件（用于其他页面）
interface StarshipProps {
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  className?: string;
}

export function Starship({ size = 'md', animated = true, className }: StarshipProps) {
  const sizes = {
    sm: { width: 100, height: 60 },
    md: { width: 150, height: 90 },
    lg: { width: 200, height: 120 }
  };
  
  return (
    <motion.div
      className={cn('relative', className)}
      animate={animated ? {
        y: [0, -5, 0],
        rotateY: [0, 2, 0]
      } : {}}
      transition={animated ? {
        duration: 3,
        repeat: Infinity,
        ease: 'easeInOut'
      } : {}}
    >
      <svg
        width={sizes[size].width}
        height={sizes[size].height}
        viewBox="0 0 200 120"
        className="filter drop-shadow-lg"
      >
        <path
          d="M20 60 L160 40 L180 60 L160 80 Z"
          fill="url(#shipGradient)"
          stroke="#ffd700"
          strokeWidth="2"
        />
        
        <circle
          cx="25"
          cy="60"
          r="15"
          fill="url(#engineGradient)"
          stroke="#00bfff"
          strokeWidth="2"
        />
        
        <rect
          x="140"
          y="50"
          width="30"
          height="20"
          rx="5"
          fill="url(#bridgeGradient)"
          stroke="#ffd700"
          strokeWidth="1"
        />
        
        <defs>
          <linearGradient id="shipGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#1e3a8a" />
            <stop offset="50%" stopColor="#3b82f6" />
            <stop offset="100%" stopColor="#1e40af" />
          </linearGradient>
          
          <radialGradient id="engineGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#00bfff" />
            <stop offset="100%" stopColor="#0066cc" />
          </radialGradient>
          
          <linearGradient id="bridgeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#ffd700" />
            <stop offset="100%" stopColor="#ffb700" />
          </linearGradient>
        </defs>
      </svg>
      
      {/* 引擎光效 */}
      {animated && (
        <motion.div
          className="absolute left-2 top-1/2 transform -translate-y-1/2 w-6 h-6"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.8, 0.4, 0.8]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        >
          <div
            className="w-full h-full rounded-full bg-blue-400"
            style={{
              boxShadow: '0 0 15px #00bfff, 0 0 30px #00bfff60'
            }}
          />
        </motion.div>
      )}
    </motion.div>
  );
}