import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface GlowButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
}

export function GlowButton({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className
}: GlowButtonProps) {
  const baseClasses = 'relative overflow-hidden font-medium transition-all duration-300 border-2 backdrop-blur-sm';
  
  const variantClasses = {
    primary: 'border-yellow-400 text-yellow-400 hover:text-black hover:bg-yellow-400',
    secondary: 'border-gray-300 text-gray-300 hover:text-black hover:bg-gray-300',
    accent: 'border-blue-400 text-blue-400 hover:text-black hover:bg-blue-400'
  };
  
  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  };
  
  const disabledClasses = 'opacity-50 cursor-not-allowed';
  
  return (
    <motion.button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        disabled && disabledClasses,
        className
      )}
      onClick={disabled ? undefined : onClick}
      whileHover={disabled ? {} : { scale: 1.05 }}
      whileTap={disabled ? {} : { scale: 0.95 }}
      disabled={disabled}
    >
      {/* 发光效果背景 */}
      <motion.div
        className="absolute inset-0 opacity-0 transition-opacity duration-300"
        style={{
          background: variant === 'primary' 
            ? 'linear-gradient(45deg, rgba(255, 212, 0, 0.2), rgba(255, 237, 78, 0.2))'
            : variant === 'secondary'
            ? 'linear-gradient(45deg, rgba(232, 232, 232, 0.2), rgba(255, 255, 255, 0.2))'
            : 'linear-gradient(45deg, rgba(0, 191, 255, 0.2), rgba(0, 128, 255, 0.2))'
        }}
        whileHover={{ opacity: 1 }}
      />
      
      {/* 边框发光效果 */}
      <motion.div
        className="absolute inset-0 rounded-inherit"
        style={{
          boxShadow: `0 0 20px ${variant === 'primary' ? '#ffd700' : variant === 'secondary' ? '#e8e8e8' : '#00bfff'}40`
        }}
        initial={{ opacity: 0 }}
        whileHover={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      />
      
      {/* 按钮内容 */}
      <span className="relative z-10">{children}</span>
      
      {/* 粒子效果 */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        initial={{ opacity: 0 }}
        whileHover={{ opacity: 1 }}
      >
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-1 h-1 rounded-full ${
              variant === 'primary' ? 'bg-yellow-400' : 
              variant === 'secondary' ? 'bg-gray-300' : 'bg-blue-400'
            }`}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 1, 0]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: i * 0.3
            }}
          />
        ))}
      </motion.div>
    </motion.button>
  );
}