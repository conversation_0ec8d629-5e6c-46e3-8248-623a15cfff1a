import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface HologramCardProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md' | 'lg';
  glowIntensity?: 'low' | 'medium' | 'high';
  interactive?: boolean;
  className?: string;
  onClick?: () => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

export function HologramCard({
  children,
  variant = 'primary',
  size = 'md',
  glowIntensity = 'medium',
  interactive = true,
  className,
  onClick,
  onMouseEnter,
  onMouseLeave
}: HologramCardProps) {
  const baseClasses = 'relative overflow-hidden backdrop-blur-md border-2 rounded-lg';
  
  const variantClasses = {
    primary: 'border-yellow-400/50 bg-yellow-400/5',
    secondary: 'border-gray-300/50 bg-gray-300/5',
    accent: 'border-blue-400/50 bg-blue-400/5'
  };
  
  const sizeClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };
  
  const glowColors = {
    primary: '#ffd700',
    secondary: '#e8e8e8',
    accent: '#00bfff'
  };
  
  const glowIntensities = {
    low: '20',
    medium: '40',
    high: '60'
  };
  
  return (
    <motion.div
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        interactive && 'cursor-pointer',
        className
      )}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      initial={{ opacity: 0, y: 20, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      whileHover={interactive ? {
        scale: 1.02,
        y: -5
      } : {}}
      transition={{ duration: 0.3, ease: 'easeOut' }}
    >
      {/* 边框发光效果 */}
      <motion.div
        className="absolute inset-0 rounded-lg pointer-events-none"
        style={{
          boxShadow: `0 0 ${glowIntensities[glowIntensity]}px ${glowColors[variant]}80`
        }}
        animate={{
          opacity: [0.5, 1, 0.5]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
      />
      
      {/* 扫描线效果 */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        style={{
          background: `linear-gradient(90deg, transparent 0%, ${glowColors[variant]}40 50%, transparent 100%)`
        }}
        initial={{ x: '-100%' }}
        animate={{ x: '100%' }}
        transition={{
          duration: 3,
          repeat: Infinity,
          repeatDelay: 5,
          ease: 'linear'
        }}
      />
      
      {/* 网格背景 */}
      <div 
        className="absolute inset-0 opacity-10 pointer-events-none"
        style={{
          backgroundImage: `
            linear-gradient(${glowColors[variant]} 1px, transparent 1px),
            linear-gradient(90deg, ${glowColors[variant]} 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px'
        }}
      />
      
      {/* 角落装饰 */}
      <div className="absolute top-2 left-2 w-4 h-4 pointer-events-none">
        <div 
          className="w-full h-0.5"
          style={{ backgroundColor: glowColors[variant] }}
        />
        <div 
          className="w-0.5 h-full"
          style={{ backgroundColor: glowColors[variant] }}
        />
      </div>
      
      <div className="absolute top-2 right-2 w-4 h-4 pointer-events-none">
        <div 
          className="w-full h-0.5"
          style={{ backgroundColor: glowColors[variant] }}
        />
        <div 
          className="w-0.5 h-full ml-auto"
          style={{ backgroundColor: glowColors[variant] }}
        />
      </div>
      
      <div className="absolute bottom-2 left-2 w-4 h-4 pointer-events-none">
        <div 
          className="w-0.5 h-full"
          style={{ backgroundColor: glowColors[variant] }}
        />
        <div 
          className="w-full h-0.5 mt-auto"
          style={{ backgroundColor: glowColors[variant] }}
        />
      </div>
      
      <div className="absolute bottom-2 right-2 w-4 h-4 pointer-events-none">
        <div 
          className="w-0.5 h-full ml-auto"
          style={{ backgroundColor: glowColors[variant] }}
        />
        <div 
          className="w-full h-0.5 mt-auto"
          style={{ backgroundColor: glowColors[variant] }}
        />
      </div>
      
      {/* 内容区域 */}
      <div className="relative z-10">
        {children}
      </div>
      
      {/* 悬停时的粒子效果 */}
      {interactive && (
        <motion.div
          className="absolute inset-0 pointer-events-none"
          initial={{ opacity: 0 }}
          whileHover={{ opacity: 1 }}
        >
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 rounded-full"
              style={{
                backgroundColor: glowColors[variant],
                left: `${10 + (i % 4) * 25}%`,
                top: `${10 + Math.floor(i / 4) * 80}%`
              }}
              animate={{
                scale: [0, 1, 0],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.1
              }}
            />
          ))}
        </motion.div>
      )}
    </motion.div>
  );
}

// 船员档案卡片组件
interface CrewCardProps {
  name: string;
  position: string;
  description: string;
  avatar?: string;
  variant?: 'primary' | 'secondary' | 'accent';
  className?: string;
}

export function CrewCard({
  name,
  position,
  description,
  avatar,
  variant = 'primary',
  className
}: CrewCardProps) {
  const [showDescription, setShowDescription] = React.useState(false);
  
  return (
    <HologramCard
      variant={variant}
      size="md"
      className={cn('min-h-[200px]', className)}
      onMouseEnter={() => setShowDescription(true)}
      onMouseLeave={() => setShowDescription(false)}
    >
      <div className="flex flex-col items-center text-center space-y-4">
        {/* 头像区域 */}
        <div className="relative">
          {avatar ? (
            <img 
              src={avatar} 
              alt={name}
              className="w-16 h-16 rounded-full border-2 border-current"
            />
          ) : (
            <div className="w-16 h-16 rounded-full border-2 border-current flex items-center justify-center text-2xl font-bold">
              {name.charAt(0)}
            </div>
          )}
          
          {/* 状态指示器 */}
          <motion.div
            className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-green-400"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{
              duration: 2,
              repeat: Infinity
            }}
          />
        </div>
        
        {/* 基本信息 */}
        <div className="space-y-2">
          <h3 className="text-lg font-bold">{name}</h3>
          <p className="text-sm opacity-80">{position}</p>
        </div>
        
        {/* 描述信息 */}
        <motion.div
          className="text-xs opacity-70 leading-relaxed"
          initial={{ opacity: 0, height: 0 }}
          animate={{
            opacity: showDescription ? 1 : 0,
            height: showDescription ? 'auto' : 0
          }}
          transition={{ duration: 0.3 }}
        >
          {description}
        </motion.div>
      </div>
    </HologramCard>
  );
}