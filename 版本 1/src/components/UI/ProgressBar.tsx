import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface ProgressBarProps {
  progress: number; // 0-100
  variant?: 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  showPercentage?: boolean;
  label?: string;
  className?: string;
}

export function ProgressBar({
  progress,
  variant = 'primary',
  size = 'md',
  animated = true,
  showPercentage = true,
  label,
  className
}: ProgressBarProps) {
  const baseClasses = 'relative overflow-hidden rounded-full border';
  
  const variantClasses = {
    primary: 'border-yellow-400/50 bg-yellow-400/10',
    secondary: 'border-gray-300/50 bg-gray-300/10',
    accent: 'border-blue-400/50 bg-blue-400/10'
  };
  
  const sizeClasses = {
    sm: 'h-2',
    md: 'h-4',
    lg: 'h-6'
  };
  
  const fillColors = {
    primary: '#ffd700',
    secondary: '#e8e8e8',
    accent: '#00bfff'
  };
  
  const clampedProgress = Math.max(0, Math.min(100, progress));
  
  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <div className="flex justify-between items-center text-sm">
          <span>{label}</span>
          {showPercentage && (
            <span className="font-mono">{Math.round(clampedProgress)}%</span>
          )}
        </div>
      )}
      
      <div className={cn(baseClasses, variantClasses[variant], sizeClasses[size])}>
        {/* 进度填充 */}
        <motion.div
          className="h-full rounded-full relative overflow-hidden"
          style={{
            background: `linear-gradient(90deg, ${fillColors[variant]}, ${fillColors[variant]}cc)`
          }}
          initial={{ width: 0 }}
          animate={{ width: `${clampedProgress}%` }}
          transition={animated ? { duration: 0.8, ease: 'easeOut' } : { duration: 0 }}
        >
          {/* 发光效果 */}
          <motion.div
            className="absolute inset-0"
            style={{
              background: `linear-gradient(90deg, transparent, ${fillColors[variant]}80, transparent)`,
              filter: 'blur(4px)'
            }}
            animate={{
              x: ['-100%', '100%']
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'linear'
            }}
          />
        </motion.div>
        
        {/* 边框发光 */}
        <motion.div
          className="absolute inset-0 rounded-full pointer-events-none"
          style={{
            boxShadow: `0 0 10px ${fillColors[variant]}40`
          }}
          animate={{
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity
          }}
        />
      </div>
    </div>
  );
}

// 圆形进度条
interface CircularProgressProps {
  progress: number; // 0-100
  size?: number;
  strokeWidth?: number;
  variant?: 'primary' | 'secondary' | 'accent';
  showPercentage?: boolean;
  label?: string;
  className?: string;
}

export function CircularProgress({
  progress,
  size = 120,
  strokeWidth = 8,
  variant = 'primary',
  showPercentage = true,
  label,
  className
}: CircularProgressProps) {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const clampedProgress = Math.max(0, Math.min(100, progress));
  const strokeDashoffset = circumference - (clampedProgress / 100) * circumference;
  
  const colors = {
    primary: '#ffd700',
    secondary: '#e8e8e8',
    accent: '#00bfff'
  };
  
  return (
    <div className={cn('flex flex-col items-center space-y-2', className)}>
      <div className="relative" style={{ width: size, height: size }}>
        <svg
          width={size}
          height={size}
          className="transform -rotate-90"
        >
          {/* 背景圆环 */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={`${colors[variant]}20`}
            strokeWidth={strokeWidth}
            fill="none"
          />
          
          {/* 进度圆环 */}
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={colors[variant]}
            strokeWidth={strokeWidth}
            fill="none"
            strokeLinecap="round"
            strokeDasharray={circumference}
            initial={{ strokeDashoffset: circumference }}
            animate={{ strokeDashoffset }}
            transition={{ duration: 1, ease: 'easeOut' }}
            style={{
              filter: `drop-shadow(0 0 8px ${colors[variant]}80)`
            }}
          />
        </svg>
        
        {/* 中心内容 */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          {showPercentage && (
            <span className="text-2xl font-bold font-mono">
              {Math.round(clampedProgress)}%
            </span>
          )}
        </div>
      </div>
      
      {label && (
        <span className="text-sm text-center">{label}</span>
      )}
    </div>
  );
}

// 加载动画组件
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'accent';
  className?: string;
}

export function LoadingSpinner({
  size = 'md',
  variant = 'primary',
  className
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };
  
  const colors = {
    primary: '#ffd700',
    secondary: '#e8e8e8',
    accent: '#00bfff'
  };
  
  return (
    <motion.div
      className={cn('relative', sizeClasses[size], className)}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: 'linear'
      }}
    >
      <div
        className="absolute inset-0 rounded-full border-2 border-transparent"
        style={{
          borderTopColor: colors[variant],
          borderRightColor: `${colors[variant]}60`
        }}
      />
      
      {/* 发光效果 */}
      <motion.div
        className="absolute inset-0 rounded-full"
        style={{
          boxShadow: `0 0 20px ${colors[variant]}40`
        }}
        animate={{
          opacity: [0.5, 1, 0.5]
        }}
        transition={{
          duration: 1,
          repeat: Infinity
        }}
      />
    </motion.div>
  );
}

// 脉冲加载动画
export function PulseLoader({ variant = 'primary', className }: {
  variant?: 'primary' | 'secondary' | 'accent';
  className?: string;
}) {
  const colors = {
    primary: '#ffd700',
    secondary: '#e8e8e8',
    accent: '#00bfff'
  };
  
  return (
    <div className={cn('flex space-x-2', className)}>
      {[0, 1, 2].map(i => (
        <motion.div
          key={i}
          className="w-3 h-3 rounded-full"
          style={{ backgroundColor: colors[variant] }}
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: i * 0.2
          }}
        />
      ))}
    </div>
  );
}