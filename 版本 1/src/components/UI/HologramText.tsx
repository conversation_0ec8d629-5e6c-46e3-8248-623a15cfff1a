import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface HologramTextProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
  glitch?: boolean;
  className?: string;
}

export function HologramText({
  children,
  variant = 'primary',
  size = 'md',
  animated = true,
  glitch = false,
  className
}: HologramTextProps) {
  const baseClasses = 'font-mono relative inline-block';
  
  const variantClasses = {
    primary: 'text-yellow-400',
    secondary: 'text-gray-300',
    accent: 'text-blue-400'
  };
  
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-xl',
    xl: 'text-3xl'
  };
  
  const glowColor = {
    primary: '#ffd700',
    secondary: '#e8e8e8',
    accent: '#00bfff'
  };
  
  return (
    <motion.div
      className={cn(baseClasses, variantClasses[variant], sizeClasses[size], className)}
      initial={animated ? { opacity: 0, y: 20 } : {}}
      animate={animated ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      {/* 主文字 */}
      <motion.span
        className="relative z-10"
        style={{
          textShadow: `0 0 10px ${glowColor[variant]}80, 0 0 20px ${glowColor[variant]}40, 0 0 30px ${glowColor[variant]}20`
        }}
        animate={glitch ? {
          x: [0, -2, 2, 0],
          textShadow: [
            `0 0 10px ${glowColor[variant]}80`,
            `2px 0 10px #ff0000, -2px 0 10px #00ff00`,
            `0 0 10px ${glowColor[variant]}80`
          ]
        } : {}}
        transition={glitch ? {
          duration: 0.2,
          repeat: Infinity,
          repeatDelay: 3
        } : {}}
      >
        {children}
      </motion.span>
      
      {/* 扫描线效果 */}
      {animated && (
        <motion.div
          className="absolute inset-0 pointer-events-none"
          style={{
            background: `linear-gradient(90deg, transparent 0%, ${glowColor[variant]}40 50%, transparent 100%)`
          }}
          initial={{ x: '-100%' }}
          animate={{ x: '100%' }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatDelay: 4,
            ease: 'linear'
          }}
        />
      )}
      
      {/* 背景发光 */}
      <motion.div
        className="absolute inset-0 rounded"
        style={{
          background: `radial-gradient(ellipse at center, ${glowColor[variant]}10 0%, transparent 70%)`,
          filter: 'blur(10px)'
        }}
        animate={{
          opacity: [0.3, 0.6, 0.3]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
      />
      
      {/* 数字雨效果 */}
      {animated && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className={`absolute text-xs opacity-30 ${variantClasses[variant]}`}
              style={{
                left: `${20 + i * 30}%`,
                fontFamily: 'monospace'
              }}
              initial={{ y: '-100%', opacity: 0 }}
              animate={{ y: '200%', opacity: [0, 0.3, 0] }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: i * 0.5,
                ease: 'linear'
              }}
            >
              {Math.random().toString(36).substring(2, 8)}
            </motion.div>
          ))}
        </div>
      )}
    </motion.div>
  );
}

// 打字机效果组件
interface TypewriterTextProps {
  text: string;
  speed?: number;
  variant?: 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  onComplete?: () => void;
  className?: string;
}

export function TypewriterText({
  text,
  speed = 50,
  variant = 'primary',
  size = 'md',
  onComplete,
  className
}: TypewriterTextProps) {
  const [displayText, setDisplayText] = React.useState('');
  const [currentIndex, setCurrentIndex] = React.useState(0);
  
  React.useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);
      
      return () => clearTimeout(timer);
    } else if (onComplete) {
      onComplete();
    }
  }, [currentIndex, text, speed, onComplete]);
  
  return (
    <HologramText
      variant={variant}
      size={size}
      animated={false}
      className={className}
    >
      {displayText}
      <motion.span
        className="inline-block w-2 h-5 ml-1 bg-current"
        animate={{ opacity: [1, 0] }}
        transition={{ duration: 0.5, repeat: Infinity }}
      />
    </HologramText>
  );
}