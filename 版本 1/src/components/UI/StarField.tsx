import React, { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';

interface Star {
  x: number;
  y: number;
  z: number;
  size: number;
  speed: number;
  opacity: number;
}

interface StarFieldProps {
  starCount?: number;
  speed?: number;
  className?: string;
}

export function StarField({ starCount = 200, speed = 0.5, className }: StarFieldProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const starsRef = useRef<Star[]>([]);
  const animationRef = useRef<number>();
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // 设置画布尺寸
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // 初始化星星
    const initStars = () => {
      starsRef.current = [];
      for (let i = 0; i < starCount; i++) {
        starsRef.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          z: Math.random() * 1000,
          size: Math.random() * 2 + 0.5,
          speed: Math.random() * speed + 0.1,
          opacity: Math.random() * 0.8 + 0.2
        });
      }
    };
    
    initStars();
    
    // 动画循环
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      starsRef.current.forEach(star => {
        // 更新星星位置
        star.z -= star.speed;
        
        // 重置超出边界的星星
        if (star.z <= 0) {
          star.z = 1000;
          star.x = Math.random() * canvas.width;
          star.y = Math.random() * canvas.height;
        }
        
        // 计算3D投影
        const x = (star.x - canvas.width / 2) * (1000 / star.z) + canvas.width / 2;
        const y = (star.y - canvas.height / 2) * (1000 / star.z) + canvas.height / 2;
        const size = star.size * (1000 / star.z);
        const opacity = star.opacity * (1000 / star.z) / 1000;
        
        // 绘制星星
        if (x >= 0 && x <= canvas.width && y >= 0 && y <= canvas.height) {
          ctx.beginPath();
          ctx.arc(x, y, size, 0, Math.PI * 2);
          ctx.fillStyle = `rgba(255, 255, 255, ${Math.min(opacity, 1)})`;
          ctx.fill();
          
          // 添加发光效果
          if (size > 1) {
            ctx.beginPath();
            ctx.arc(x, y, size * 2, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(255, 255, 255, ${Math.min(opacity * 0.3, 0.3)})`;
            ctx.fill();
          }
        }
      });
      
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animate();
    
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [starCount, speed]);
  
  return (
    <canvas
      ref={canvasRef}
      className={`fixed inset-0 pointer-events-none ${className}`}
      style={{ zIndex: -1 }}
    />
  );
}

// 静态星空背景组件（CSS版本，性能更好）
interface StaticStarFieldProps {
  density?: 'low' | 'medium' | 'high';
  animated?: boolean;
  className?: string;
}

export function StaticStarField({ 
  density = 'medium', 
  animated = true, 
  className 
}: StaticStarFieldProps) {
  const starCounts = {
    low: 50,
    medium: 100,
    high: 200
  };
  
  const stars = React.useMemo(() => {
    return Array.from({ length: starCounts[density] }, (_, i) => ({
      id: i,
      left: Math.random() * 100,
      top: Math.random() * 100,
      size: Math.random() * 3 + 1,
      animationDelay: Math.random() * 3,
      animationDuration: Math.random() * 3 + 2
    }));
  }, [density]);
  
  return (
    <div className={`fixed inset-0 overflow-hidden pointer-events-none ${className}`}>
      {stars.map(star => (
        <motion.div
          key={star.id}
          className="absolute bg-white rounded-full"
          style={{
            left: `${star.left}%`,
            top: `${star.top}%`,
            width: `${star.size}px`,
            height: `${star.size}px`,
            boxShadow: `0 0 ${star.size * 2}px rgba(255, 255, 255, 0.5)`
          }}
          animate={animated ? {
            opacity: [0.2, 1, 0.2],
            scale: [0.8, 1.2, 0.8]
          } : {}}
          transition={animated ? {
            duration: star.animationDuration,
            repeat: Infinity,
            delay: star.animationDelay,
            ease: 'easeInOut'
          } : {}}
        />
      ))}
    </div>
  );
}

// 星云背景组件
interface NebulaProps {
  colors?: string[];
  intensity?: number;
  className?: string;
}

export function Nebula({ 
  colors = ['#4f46e5', '#7c3aed', '#db2777'], 
  intensity = 0.3,
  className 
}: NebulaProps) {
  return (
    <div className={`fixed inset-0 pointer-events-none ${className}`}>
      {colors.map((color, index) => (
        <motion.div
          key={index}
          className="absolute inset-0"
          style={{
            background: `radial-gradient(ellipse at ${30 + index * 20}% ${40 + index * 15}%, ${color}${Math.floor(intensity * 255).toString(16).padStart(2, '0')} 0%, transparent 50%)`,
            filter: 'blur(100px)'
          }}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [intensity, intensity * 1.5, intensity]
          }}
          transition={{
            duration: 8 + index * 2,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />
      ))}
    </div>
  );
}