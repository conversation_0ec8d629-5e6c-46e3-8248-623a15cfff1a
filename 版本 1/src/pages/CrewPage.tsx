import React from 'react';
import { motion } from 'framer-motion';
import { CrewCard } from '../components/UI/HologramCard';
import { HologramText } from '../components/UI/HologramText';
import { GlowButton } from '../components/UI/GlowButton';
import { StaticStarField } from '../components/UI/StarField';
import { ParticleEffect } from '../components/Animations/ParticleEffect';
import { usePageNavigation } from '../contexts/AppContext';
import { CREW_MEMBERS } from '../lib/constants';
import { cn } from '../lib/utils';

export function CrewPage() {
  const { nextPage, prevPage } = usePageNavigation();
  const [selectedCrew, setSelectedCrew] = React.useState<number | null>(null);
  const [showCards, setShowCards] = React.useState(false);
  
  React.useEffect(() => {
    const timer = setTimeout(() => setShowCards(true), 500);
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      {/* 星空背景 */}
      <StaticStarField className="absolute inset-0" />
      
      {/* 粒子效果 */}
      <ParticleEffect
        count={30}
        colors={['#3b82f6', '#1d4ed8', '#06b6d4']}
        speed={0.5}
        direction="random"
        className="absolute inset-0"
      />
      
      {/* 舰桥背景效果 */}
      <div className="absolute inset-0">
        {/* 舰桥控制台轮廓 */}
        <motion.div
          className="absolute bottom-0 left-0 right-0 h-32"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          <div className="relative w-full h-full">
            {/* 控制台发光线条 */}
            <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-400 to-transparent" />
            <div className="absolute bottom-4 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-transparent via-cyan-400 to-transparent" />
            
            {/* 控制台指示灯 */}
            {Array.from({ length: 12 }, (_, i) => (
              <motion.div
                key={i}
                className="absolute bottom-2 w-1 h-1 rounded-full"
                style={{
                  left: `${10 + i * 7}%`,
                  backgroundColor: i % 3 === 0 ? '#22c55e' : i % 3 === 1 ? '#3b82f6' : '#f59e0b'
                }}
                animate={{
                  opacity: [0.3, 1, 0.3]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </div>
        </motion.div>
        
        {/* 全息投影网格 */}
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full" style={{
            backgroundImage: `
              linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }} />
        </div>
      </div>
      
      {/* 主要内容 */}
      <div className="relative z-10 container mx-auto px-8 py-16">
        {/* 页面标题 */}
        <motion.div
          className="text-center mb-8 sm:mb-16 z-10 relative px-4"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.8 }}
        >
          <motion.h1
            className="text-3xl-mobile sm:text-5xl md:text-7xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 mb-2 sm:mb-4"
            animate={{ 
              backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
            }}
            transition={{ duration: 4, repeat: Infinity, ease: 'linear' }}
            style={{ backgroundSize: '200% 200%' }}
          >
            船员档案
          </motion.h1>
          <motion.p
            className="text-lg-mobile sm:text-xl text-cyan-300 font-light"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 0.8 }}
          >
            星舰核心成员信息
          </motion.p>
        </motion.div>
        
        {/* 船员卡片网格 */}
        {showCards && (
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-8 max-w-7xl mx-auto px-4 sm:px-8 z-10 relative"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2, duration: 0.8 }}
          >
            {CREW_MEMBERS.map((crew, index) => (
              <motion.div
                key={crew.id}
                initial={{ opacity: 0, y: 50, scale: 0.8 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ 
                  delay: index * 0.2,
                  duration: 0.8,
                  type: 'spring',
                  stiffness: 100
                }}
                whileHover={{ scale: 1.05 }}
                onClick={() => setSelectedCrew(selectedCrew === index ? null : index)}
              >
                <CrewCard
                  name={crew.name}
                  position={crew.position}
                  description={crew.description}
                  avatar={crew.avatar}
                  variant={index % 2 === 0 ? 'primary' : 'secondary'}
                  className="w-full"
                />
              </motion.div>
            ))}
          </motion.div>
        )}
        
        {/* 选中船员的详细信息 */}
        {selectedCrew !== null && (
          <motion.div
            className="mt-16 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -30 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-black/50 border border-blue-400/30 rounded-lg p-4 sm:p-6 backdrop-blur-sm">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-3 h-3 rounded-full bg-green-400 animate-pulse" />
                <h3 className="text-xl font-bold text-blue-400">
                  {CREW_MEMBERS[selectedCrew].name} - 详细档案
                </h3>
              </div>
              
              <div className="space-y-2 sm:space-y-3 text-gray-300">
                <div>
                  <span className="text-cyan-400 font-mono text-sm sm:text-base">职位：</span>
                  <span className="text-sm sm:text-base">{CREW_MEMBERS[selectedCrew].position}</span>
                </div>
                <div>
                  <span className="text-cyan-400 font-mono text-sm sm:text-base">状态：</span>
                  <span className={cn(
                    'ml-2 px-2 py-1 rounded text-xs',
                    CREW_MEMBERS[selectedCrew].status === 'active' 
                      ? 'bg-green-400/20 text-green-400'
                      : 'bg-yellow-400/20 text-yellow-400'
                  )}>
                    {CREW_MEMBERS[selectedCrew].status === 'active' ? '在线' : '待命'}
                  </span>
                </div>
                <div>
                  <span className="text-cyan-400 font-mono text-sm sm:text-base">描述：</span>
                  <p className="mt-2 leading-relaxed text-sm sm:text-base">
                    {CREW_MEMBERS[selectedCrew].description}
                  </p>
                </div>
              </div>
              
              {/* 关闭按钮 */}
              <motion.button
                className="mt-4 px-3 py-2 sm:px-4 sm:py-2 bg-blue-600/20 border border-blue-400/50 rounded text-blue-400 hover:bg-blue-600/30 transition-colors text-sm sm:text-base"
                onClick={() => setSelectedCrew(null)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                关闭档案
              </motion.button>
            </div>
          </motion.div>
        )}
        
        {/* 导航按钮 */}
        <motion.div
          className="flex justify-between items-center mt-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2, duration: 0.8 }}
        >
          <GlowButton
            variant="secondary"
            size="md"
            onClick={prevPage}
            className="px-6 py-3"
          >
            ← 返回封面
          </GlowButton>
          
          <div className="text-center">
            <div className="text-green-400 text-sm font-mono mb-2">
              船员状态检查完成
            </div>
            <div className="flex space-x-1">
              {CREW_MEMBERS.map((_, index) => (
                <motion.div
                  key={index}
                  className="w-2 h-2 rounded-full bg-green-400"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 1 + index * 0.1 }}
                />
              ))}
            </div>
          </div>
          
          <GlowButton
            variant="primary"
            size="md"
            onClick={nextPage}
            className="px-6 py-3"
          >
            航行日志 →
          </GlowButton>
        </motion.div>
      </div>
      
      {/* 全息扫描线效果 */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: [0, 0.3, 0] }}
        transition={{
          duration: 3,
          repeat: Infinity,
          repeatDelay: 5
        }}
      >
        <div className="w-full h-0.5 bg-gradient-to-r from-transparent via-cyan-400 to-transparent" 
             style={{ 
               position: 'absolute',
               top: '50%',
               transform: 'translateY(-50%)'
             }} />
      </motion.div>
    </div>
  );
}