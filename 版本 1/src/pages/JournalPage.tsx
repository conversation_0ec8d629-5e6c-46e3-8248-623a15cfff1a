import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { HologramText } from '../components/UI/HologramText';
import { GlowButton } from '../components/UI/GlowButton';
import { StaticStarField } from '../components/UI/StarField';
import { ParticleEffect } from '../components/Animations/ParticleEffect';
import { usePageNavigation } from '../contexts/AppContext';
import { MEMORIES } from '../lib/constants';
import { cn } from '../lib/utils';

export function JournalPage() {
  const { nextPage, prevPage } = usePageNavigation();
  const [selectedMemory, setSelectedMemory] = React.useState<number | null>(null);
  const [showStarClusters, setShowStarClusters] = React.useState(false);
  
  React.useEffect(() => {
    const timer = setTimeout(() => setShowStarClusters(true), 1000);
    return () => clearTimeout(timer);
  }, []);
  
  const starClusterPositions = [
    { top: '25%', left: '20%' },
    { top: '45%', left: '65%' },
    { top: '70%', left: '35%' }
  ];
  
  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      {/* 深空星图背景 */}
      <StaticStarField className="absolute inset-0" density="high" />
      
      {/* 星云效果 */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/3 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 w-48 h-48 bg-blue-500/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-cyan-500/10 rounded-full blur-2xl" />
      </div>
      
      {/* 航行轨迹线 */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none">
        <defs>
          <linearGradient id="trailGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6" stopOpacity="0" />
            <stop offset="50%" stopColor="#06b6d4" stopOpacity="0.6" />
            <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0" />
          </linearGradient>
        </defs>
        
        <motion.path
          d="M 100 200 Q 300 100 500 300 T 800 400 Q 1000 500 1200 300"
          stroke="url(#trailGradient)"
          strokeWidth="2"
          fill="none"
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{ pathLength: 1, opacity: 1 }}
          transition={{ duration: 3, delay: 0.5 }}
        />
        
        <motion.path
          d="M 200 500 Q 400 300 600 600 T 900 500 Q 1100 400 1300 600"
          stroke="url(#trailGradient)"
          strokeWidth="1.5"
          fill="none"
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{ pathLength: 1, opacity: 1 }}
          transition={{ duration: 3, delay: 1 }}
        />
      </svg>
      
      {/* 粒子效果 */}
      <ParticleEffect
        count={40}
        colors={['#8b5cf6', '#06b6d4', '#3b82f6']}
        speed={0.3}
        direction="random"
        className="absolute inset-0"
      />
      
      {/* 主要内容 */}
      <div className="relative z-10 container mx-auto px-8 py-16">
        {/* 页面标题 */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          <HologramText
            variant="primary"
            size="xl"
            className="text-4xl md:text-6xl font-bold mb-4"
          >
            航行日志：闪亮的星尘记忆
          </HologramText>
          
          <motion.p
            className="text-cyan-400 text-lg font-mono"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            星历 2024.11 - 深空探索记录
          </motion.p>
        </motion.div>
        
        {/* 星图区域 */}
        <div className="relative max-w-6xl mx-auto h-96 md:h-[500px] mb-16">
          {/* 星图网格 */}
          <div className="absolute inset-0 opacity-20">
            <div className="w-full h-full" style={{
              backgroundImage: `
                linear-gradient(rgba(6, 182, 212, 0.3) 1px, transparent 1px),
                linear-gradient(90deg, rgba(6, 182, 212, 0.3) 1px, transparent 1px)
              `,
              backgroundSize: '40px 40px'
            }} />
          </div>
          
          {/* 星团标记 */}
          {showStarClusters && MEMORIES.map((memory, index) => (
            <motion.div
              key={memory.id}
              className="absolute cursor-pointer group"
              style={starClusterPositions[index]}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ 
                delay: 1.5 + index * 0.5,
                duration: 0.8,
                type: 'spring'
              }}
              whileHover={{ scale: 1.2 }}
              onClick={() => setSelectedMemory(selectedMemory === index ? null : index)}
            >
              {/* 星团核心 */}
              <div className="relative">
                <motion.div
                  className={cn(
                    'w-8 h-8 rounded-full border-2 backdrop-blur-sm',
                    selectedMemory === index
                      ? 'bg-yellow-400/30 border-yellow-400 shadow-lg shadow-yellow-400/50'
                      : 'bg-blue-400/20 border-blue-400 shadow-lg shadow-blue-400/30'
                  )}
                  animate={{
                    boxShadow: selectedMemory === index
                      ? ['0 0 20px #facc15', '0 0 40px #facc15', '0 0 20px #facc15']
                      : ['0 0 10px #3b82f6', '0 0 20px #3b82f6', '0 0 10px #3b82f6']
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
                
                {/* 星团光环 */}
                <motion.div
                  className="absolute inset-0 rounded-full border border-cyan-400/30"
                  animate={{ scale: [1, 1.5, 1], opacity: [0.5, 0, 0.5] }}
                  transition={{ duration: 3, repeat: Infinity, delay: index * 0.5 }}
                />
                
                {/* 星团标签 */}
                <motion.div
                  className="absolute top-10 left-1/2 transform -translate-x-1/2 whitespace-nowrap"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 2 + index * 0.5 }}
                >
                  <div className="bg-black/70 border border-cyan-400/50 rounded px-3 py-1 text-sm text-cyan-400 font-mono">
                    星团 {index + 1}
                  </div>
                </motion.div>
                
                {/* 悬停提示 */}
                <motion.div
                  className="absolute -top-16 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none"
                >
                  <div className="bg-blue-900/90 border border-blue-400/50 rounded px-3 py-2 text-sm text-blue-200 whitespace-nowrap">
                    点击查看记忆
                  </div>
                </motion.div>
              </div>
            </motion.div>
          ))}
          
          {/* 连接线 */}
          <svg className="absolute inset-0 w-full h-full pointer-events-none">
            <motion.line
              x1="20%" y1="25%"
              x2="65%" y2="45%"
              stroke="#06b6d4"
              strokeWidth="1"
              strokeDasharray="5,5"
              opacity="0.4"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 2, delay: 3 }}
            />
            <motion.line
              x1="65%" y1="45%"
              x2="35%" y2="70%"
              stroke="#06b6d4"
              strokeWidth="1"
              strokeDasharray="5,5"
              opacity="0.4"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 2, delay: 3.5 }}
            />
          </svg>
        </div>
        
        {/* 记忆详情显示 */}
        <AnimatePresence>
          {selectedMemory !== null && (
            <motion.div
              className="max-w-4xl mx-auto mb-16"
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -50, scale: 0.9 }}
              transition={{ duration: 0.5 }}
            >
              <div className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 border border-cyan-400/30 rounded-lg p-8 backdrop-blur-sm">
                <div className="flex items-center space-x-4 mb-6">
                  <motion.div
                    className="w-4 h-4 rounded-full bg-yellow-400"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                  <h3 className="text-2xl font-bold text-yellow-400">
                    星团 {selectedMemory + 1} - 记忆碎片
                  </h3>
                </div>
                
                <div className="space-y-4">
                  <div className="text-cyan-400 font-mono text-sm">
                    记录时间：星历 {MEMORIES[selectedMemory].timestamp}
                  </div>
                  
                  <div className="text-gray-200 text-lg leading-relaxed">
                    {MEMORIES[selectedMemory].content}
                  </div>
                  
                  <div className="flex items-center space-x-4 mt-6">
                    <div className="text-sm text-gray-400">
                      重要程度：
                    </div>
                    <div className="flex space-x-1">
                      {Array.from({ length: 5 }, (_, i) => (
                        <motion.div
                          key={i}
                          className={cn(
                            'w-2 h-2 rounded-full',
                            i < MEMORIES[selectedMemory].importance
                              ? 'bg-yellow-400'
                              : 'bg-gray-600'
                          )}
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: i * 0.1 }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
                
                {/* 关闭按钮 */}
                <motion.button
                  className="mt-6 px-6 py-2 bg-cyan-600/20 border border-cyan-400/50 rounded text-cyan-400 hover:bg-cyan-600/30 transition-colors"
                  onClick={() => setSelectedMemory(null)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  收起记忆
                </motion.button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* 导航按钮 */}
        <motion.div
          className="flex justify-between items-center mt-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 4, duration: 0.8 }}
        >
          <GlowButton
            variant="secondary"
            size="md"
            onClick={prevPage}
            className="px-6 py-3"
          >
            ← 船员档案
          </GlowButton>
          
          <div className="text-center">
            <div className="text-cyan-400 text-sm font-mono mb-2">
              记忆扫描完成
            </div>
            <div className="flex space-x-2">
              {MEMORIES.map((_, index) => (
                <motion.div
                  key={index}
                  className={cn(
                    'w-3 h-3 rounded-full border-2',
                    selectedMemory === index
                      ? 'bg-yellow-400 border-yellow-400'
                      : 'bg-cyan-400/20 border-cyan-400'
                  )}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 2 + index * 0.2 }}
                />
              ))}
            </div>
          </div>
          
          <GlowButton
            variant="primary"
            size="md"
            onClick={nextPage}
            className="px-6 py-3"
          >
            小行星带 →
          </GlowButton>
        </motion.div>
      </div>
      
      {/* 深空扫描效果 */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: [0, 0.2, 0] }}
        transition={{
          duration: 4,
          repeat: Infinity,
          repeatDelay: 8
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400/10 to-transparent transform rotate-45" />
      </motion.div>
    </div>
  );
}