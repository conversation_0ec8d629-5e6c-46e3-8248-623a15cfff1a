import React from 'react';
import { motion } from 'framer-motion';
import { StarshipJump } from '../components/Animations/StarshipJump';
import { HologramText, TypewriterText } from '../components/UI/HologramText';
import { GlowButton } from '../components/UI/GlowButton';
import { StaticStarField } from '../components/UI/StarField';
import { usePageNavigation } from '../contexts/AppContext';
import { cn } from '../lib/utils';

export function CoverPage() {
  const { nextPage } = usePageNavigation();
  const [showContent, setShowContent] = React.useState(false);
  const [showButton, setShowButton] = React.useState(false);
  
  React.useEffect(() => {
    // 延迟显示内容，让星舰动画先播放
    const contentTimer = setTimeout(() => setShowContent(true), 2000);
    const buttonTimer = setTimeout(() => setShowButton(true), 4000);
    
    return () => {
      clearTimeout(contentTimer);
      clearTimeout(buttonTimer);
    };
  }, []);
  
  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      {/* 星空背景 */}
      <StaticStarField className="absolute inset-0" />
      
      {/* 星舰跳跃动画 */}
      <StarshipJump className="absolute inset-0" />
      
      {/* 福建地图轮廓发光效果 */}
      <motion.div
        className="absolute bottom-20 right-4 sm:right-20 w-24 h-24 sm:w-32 sm:h-32"
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 3, duration: 1 }}
      >
        <div className="relative w-full h-full">
          {/* 简化的福建地图轮廓 */}
          <svg
            viewBox="0 0 100 120"
            className="w-full h-full"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <motion.path
              d="M20 20 L80 15 L85 30 L75 45 L80 60 L70 75 L65 90 L50 100 L35 95 L25 80 L15 65 L20 45 Z"
              stroke="#3b82f6"
              strokeWidth="2"
              fill="rgba(59, 130, 246, 0.1)"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ pathLength: 1, opacity: 1 }}
              transition={{ delay: 3.5, duration: 2 }}
            />
            
            {/* 发光点 */}
            {[
              { x: 40, y: 50 },
              { x: 55, y: 40 },
              { x: 45, y: 70 },
              { x: 60, y: 65 }
            ].map((point, index) => (
              <motion.circle
                key={index}
                cx={point.x}
                cy={point.y}
                r="2"
                fill="#3b82f6"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ 
                  opacity: [0, 1, 0.7],
                  scale: [0, 1.5, 1]
                }}
                transition={{ 
                  delay: 4 + index * 0.2,
                  duration: 0.8
                }}
              />
            ))}
          </svg>
          
          {/* 地图发光效果 */}
          <motion.div
            className="absolute inset-0 rounded-lg"
            style={{
              background: 'radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%)',
              filter: 'blur(10px)'
            }}
            animate={{
              opacity: [0.3, 0.7, 0.3]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: 4
            }}
          />
        </div>
        
        {/* 地图标签 */}
        <motion.div
          className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-blue-400 text-sm font-mono"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 5, duration: 0.5 }}
        >
          福建
        </motion.div>
      </motion.div>
      
      {/* 主要内容 */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-8">
        {showContent && (
          <motion.div
            className="text-center space-y-8 max-w-4xl"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            {/* 主标题 */}
            <div className="space-y-4">
              <HologramText
                variant="primary"
                size="xl"
                className="text-4xl-mobile sm:text-6xl md:text-8xl font-bold"
              >
                任务报告
              </HologramText>
              
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1, duration: 0.8 }}
              >
                <HologramText
                  variant="accent"
                  size="lg"
                  className="text-2xl sm:text-4xl md:text-6xl font-semibold text-yellow-400"
                >
                  "星光守护者"计划
                </HologramText>
              </motion.div>
            </div>
            
            {/* 副标题 */}
            <motion.div
              className="space-y-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2, duration: 1 }}
            >
              <TypewriterText
                text="福建大学生安全知识竞赛"
                speed={100}
                className="text-xl md:text-2xl text-blue-300 font-mono"
              />
              
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 3, duration: 0.5 }}
              >
                <TypewriterText
                  text="航行复盘"
                  speed={150}
                  className="text-lg md:text-xl text-cyan-400 font-mono"
                />
              </motion.div>
            </motion.div>
            
            {/* 装饰性元素 */}
            <motion.div
              className="flex justify-center space-x-8 mt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 3.5, duration: 0.8 }}
            >
              {['◆', '◇', '◆'].map((symbol, index) => (
                <motion.span
                  key={index}
                  className="text-2xl text-yellow-400"
                  animate={{
                    opacity: [0.5, 1, 0.5],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: index * 0.3
                  }}
                >
                  {symbol}
                </motion.span>
              ))}
            </motion.div>
          </motion.div>
        )}
        
        {/* 开始按钮 */}
        {showButton && (
          <motion.div
            className="absolute bottom-20 left-1/2 transform -translate-x-1/2 px-4"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <GlowButton
              variant="primary"
              size="lg"
              onClick={nextPage}
              className="px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold"
            >
              开始航行复盘
            </GlowButton>
          </motion.div>
        )}
      </div>
      
      {/* 底部状态指示器 */}
      <motion.div
        className="absolute bottom-4 sm:bottom-8 right-4 sm:right-8 flex items-center space-x-2 text-green-400 text-xs sm:text-sm font-mono"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 5, duration: 0.5 }}
      >
        <motion.div
          className="w-2 h-2 rounded-full bg-green-400"
          animate={{
            opacity: [1, 0.3, 1]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity
          }}
        />
        <span>守护者号 - 系统就绪</span>
      </motion.div>
      
      {/* 粒子效果 */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 20 }, (_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-blue-400 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
            animate={{
              opacity: [0, 1, 0],
              scale: [0, 1, 0]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: Math.random() * 5
            }}
          />
        ))}
      </div>
    </div>
  );
}