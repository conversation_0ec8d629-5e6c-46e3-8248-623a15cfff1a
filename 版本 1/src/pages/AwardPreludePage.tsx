import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { HologramText, TypewriterText } from '../components/UI/HologramText';
import { GlowButton } from '../components/UI/GlowButton';
import { StaticStarField } from '../components/UI/StarField';
import { ParticleEffect } from '../components/Animations/ParticleEffect';
import { usePageNavigation } from '../contexts/AppContext';
import { PRAISE_MESSAGES } from '../lib/constants';
import { cn } from '../lib/utils';

export function AwardPreludePage() {
  const { nextPage, prevPage } = usePageNavigation();
  const [currentPraise, setCurrentPraise] = React.useState(0);
  const [showBridge, setShowBridge] = React.useState(false);
  const [showScreen, setShowScreen] = React.useState(false);
  const [isScrolling, setIsScrolling] = React.useState(false);
  
  React.useEffect(() => {
    const timer1 = setTimeout(() => setShowBridge(true), 500);
    const timer2 = setTimeout(() => setShowScreen(true), 1500);
    const timer3 = setTimeout(() => setIsScrolling(true), 2500);
    
    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, []);
  
  React.useEffect(() => {
    if (!isScrolling) return;
    
    const interval = setInterval(() => {
      setCurrentPraise(prev => (prev + 1) % PRAISE_MESSAGES.length);
    }, 3000);
    
    return () => clearInterval(interval);
  }, [isScrolling]);
  
  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      {/* 深空背景 */}
      <StaticStarField className="absolute inset-0" density="medium" />
      
      {/* 舰桥环境光效 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-blue-500/10 to-transparent" />
        <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-cyan-500/10 to-transparent" />
        <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-purple-500/10 to-transparent" />
        <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-green-500/10 to-transparent" />
      </div>
      
      {/* 舰桥结构 */}
      <AnimatePresence>
        {showBridge && (
          <motion.div
            className="absolute inset-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 2 }}
          >
            {/* 舰桥轮廓 */}
            <div className="absolute inset-0">
              {/* 控制台轮廓 */}
              <motion.div
                className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-4/5 h-32"
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5, duration: 1 }}
              >
                <div className="w-full h-full border-t-2 border-l-2 border-r-2 border-cyan-400/30 rounded-t-3xl bg-gradient-to-t from-cyan-900/20 to-transparent" />
                
                {/* 控制台指示灯 */}
                <div className="absolute top-4 left-0 w-full flex justify-around px-8">
                  {Array.from({ length: 12 }).map((_, i) => (
                    <motion.div
                      key={i}
                      className={cn(
                        'w-2 h-2 rounded-full',
                        i % 3 === 0 ? 'bg-green-400' : i % 3 === 1 ? 'bg-blue-400' : 'bg-purple-400'
                      )}
                      animate={{ opacity: [0.3, 1, 0.3] }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: i * 0.2
                      }}
                    />
                  ))}
                </div>
              </motion.div>
              
              {/* 侧面控制面板 */}
              <motion.div
                className="absolute left-8 top-1/2 transform -translate-y-1/2 w-16 h-64"
                initial={{ x: -50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.8, duration: 1 }}
              >
                <div className="w-full h-full border border-blue-400/30 rounded-lg bg-gradient-to-b from-blue-900/20 to-transparent" />
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2 space-y-3">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <motion.div
                      key={i}
                      className="w-2 h-2 rounded-full bg-blue-400"
                      animate={{ opacity: [0.3, 1, 0.3] }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        delay: i * 0.3
                      }}
                    />
                  ))}
                </div>
              </motion.div>
              
              <motion.div
                className="absolute right-8 top-1/2 transform -translate-y-1/2 w-16 h-64"
                initial={{ x: 50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.8, duration: 1 }}
              >
                <div className="w-full h-full border border-green-400/30 rounded-lg bg-gradient-to-b from-green-900/20 to-transparent" />
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2 space-y-3">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <motion.div
                      key={i}
                      className="w-2 h-2 rounded-full bg-green-400"
                      animate={{ opacity: [0.3, 1, 0.3] }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        delay: i * 0.3 + 0.5
                      }}
                    />
                  ))}
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* 主要内容 */}
      <div className="relative z-10 container mx-auto px-8 py-16">
        {/* 页面标题 */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          <motion.div
            className="flex items-center justify-center space-x-4 mb-4"
            animate={{ scale: [1, 1.02, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <motion.div
              className="w-3 h-3 rounded-full bg-yellow-400"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            />
            <span className="text-yellow-400 font-mono text-lg">◈ 舰桥广播 ◈</span>
            <motion.div
              className="w-3 h-3 rounded-full bg-yellow-400"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 0.75 }}
            />
          </motion.div>
          
          <HologramText
            variant="primary"
            size="xl"
            className="text-3xl md:text-5xl font-bold mb-8"
          >
            全舰广播：致敬我们的伙伴！
          </HologramText>
          
          <motion.p
            className="text-cyan-400 text-lg font-mono"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            来自全体船员的感谢与赞美
          </motion.p>
        </motion.div>
        
        {/* 主屏幕区域 */}
        <div className="relative max-w-5xl mx-auto mb-16">
          <AnimatePresence>
            {showScreen && (
              <motion.div
                className="relative"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 1.5, ease: 'easeOut' }}
              >
                {/* 主屏幕边框 */}
                <div className="relative bg-gradient-to-br from-blue-900/30 to-purple-900/30 border-2 border-cyan-400/50 rounded-lg p-8 backdrop-blur-sm">
                  {/* 屏幕装饰 */}
                  <div className="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-cyan-400/50" />
                  <div className="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-cyan-400/50" />
                  <div className="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-cyan-400/50" />
                  <div className="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-cyan-400/50" />
                  
                  {/* 屏幕标题 */}
                  <motion.div
                    className="text-center mb-8"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5 }}
                  >
                    <div className="text-cyan-400 font-mono text-xl mb-2">
                      守护者号 - 主显示屏
                    </div>
                    <div className="text-blue-400 font-mono text-sm">
                      船员赞美信息流 - 实时广播
                    </div>
                  </motion.div>
                  
                  {/* 滚动赞美区域 */}
                  <div className="relative h-80 overflow-hidden bg-black/30 rounded-lg border border-cyan-400/30">
                    {/* 扫描线效果 */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-400/10 to-transparent h-8"
                      animate={{ y: [0, 320, 0] }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: 'linear'
                      }}
                    />
                    
                    {/* 赞美消息 */}
                    <div className="relative z-10 p-6 h-full flex flex-col justify-center">
                      <AnimatePresence mode="wait">
                        {isScrolling && (
                          <motion.div
                            key={currentPraise}
                            className="text-center"
                            initial={{ opacity: 0, y: 50 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -50 }}
                            transition={{ duration: 0.8 }}
                          >
                            <motion.div
                              className="mb-6"
                              animate={{ scale: [1, 1.05, 1] }}
                              transition={{ duration: 2, repeat: Infinity }}
                            >
                              <div className="text-yellow-400 font-mono text-lg mb-2">
                                来自 {PRAISE_MESSAGES[currentPraise].from}
                              </div>
                              <div className="w-24 h-1 bg-gradient-to-r from-transparent via-yellow-400 to-transparent mx-auto" />
                            </motion.div>
                            
                            <TypewriterText
                              text={PRAISE_MESSAGES[currentPraise].message}
                              speed={30}
                              className="text-2xl md:text-3xl text-cyan-200 leading-relaxed font-light"
                            />
                            
                            <motion.div
                              className="mt-6 text-purple-300 font-mono text-sm"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 2 }}
                            >
                              传输时间: {new Date().toLocaleTimeString()}
                            </motion.div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                    
                    {/* 数据流效果 */}
                    <div className="absolute top-2 right-2 text-green-400 font-mono text-xs">
                      <motion.div
                        animate={{ opacity: [0.5, 1, 0.5] }}
                        transition={{ duration: 1, repeat: Infinity }}
                      >
                        ● LIVE
                      </motion.div>
                    </div>
                  </div>
                  
                  {/* 屏幕状态栏 */}
                  <motion.div
                    className="mt-4 flex justify-between items-center text-sm font-mono"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1 }}
                  >
                    <div className="text-green-400">
                      信号强度: 100% | 加密级别: 军用
                    </div>
                    <div className="text-blue-400">
                      消息 {currentPraise + 1} / {PRAISE_MESSAGES.length}
                    </div>
                  </motion.div>
                </div>
                
                {/* 屏幕光效 */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-cyan-400/10 via-transparent to-purple-400/10 rounded-lg pointer-events-none"
                  animate={{ opacity: [0.3, 0.6, 0.3] }}
                  transition={{ duration: 3, repeat: Infinity }}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        
        {/* 舰桥状态信息 */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 2, duration: 1 }}
        >
          <div className="bg-gradient-to-br from-green-900/20 to-blue-900/20 border border-green-400/30 rounded-lg p-4 backdrop-blur-sm">
            <div className="text-green-400 font-mono text-sm mb-2">舰桥状态</div>
            <div className="text-green-300 text-lg">全员就位</div>
            <div className="text-green-400 text-xs mt-1">6/6 船员在线</div>
          </div>
          
          <div className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 border border-blue-400/30 rounded-lg p-4 backdrop-blur-sm">
            <div className="text-blue-400 font-mono text-sm mb-2">通讯系统</div>
            <div className="text-blue-300 text-lg">运行正常</div>
            <div className="text-blue-400 text-xs mt-1">延迟: &lt;1ms</div>
          </div>
          
          <div className="bg-gradient-to-br from-purple-900/20 to-pink-900/20 border border-purple-400/30 rounded-lg p-4 backdrop-blur-sm">
            <div className="text-purple-400 font-mono text-sm mb-2">士气指数</div>
            <div className="text-purple-300 text-lg">极高</div>
            <div className="text-purple-400 text-xs mt-1">团队凝聚力: 98%</div>
          </div>
        </motion.div>
        
        {/* 导航按钮 */}
        <motion.div
          className="flex justify-between items-center mt-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 3, duration: 0.8 }}
        >
          <GlowButton
            variant="secondary"
            size="md"
            onClick={prevPage}
            className="px-6 py-3"
          >
            ← 效率星云
          </GlowButton>
          
          <div className="text-center">
            <div className="text-yellow-400 text-sm font-mono mb-2">
              广播进度
            </div>
            <div className="flex space-x-2">
              {PRAISE_MESSAGES.map((_, index) => (
                <motion.div
                  key={index}
                  className={cn(
                    'w-2 h-2 rounded-full',
                    index === currentPraise
                      ? 'bg-yellow-400'
                      : index < currentPraise
                      ? 'bg-green-400'
                      : 'bg-gray-600'
                  )}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 2 + index * 0.1 }}
                />
              ))}
            </div>
          </div>
          
          <GlowButton
            variant="primary"
            size="md"
            onClick={nextPage}
            className="px-6 py-3"
          >
            星光勋章 →
          </GlowButton>
        </motion.div>
      </div>
      
      {/* 舰桥粒子效果 */}
      <ParticleEffect
        count={30}
        colors={['#06b6d4', '#3b82f6', '#8b5cf6', '#10b981']}
        speed={0.2}
        direction="up"
        className="absolute inset-0"
      />
    </div>
  );
}