import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { HologramText } from '../components/UI/HologramText';
import { GlowButton } from '../components/UI/GlowButton';
import { StaticStarField } from '../components/UI/StarField';
import { ConvergingBeams } from '../components/Animations/LightBeam';
import { ParticleEffect, ExplosionParticles } from '../components/Animations/ParticleEffect';
import { usePageNavigation } from '../contexts/AppContext';
import { CREW_MEMBERS } from '../lib/constants';
import { cn } from '../lib/utils';

interface AwardStage {
  id: string;
  title: string;
  duration: number;
}

const AWARD_STAGES: AwardStage[] = [
  { id: 'announcement', title: '本航程的"星光勋章"获得者是……', duration: 3000 },
  { id: 'names', title: '显示所有船员名单', duration: 2000 },
  { id: 'selection', title: '光束选择获奖者', duration: 4000 },
  { id: 'medal', title: '勋章凝聚', duration: 3000 },
  { id: 'celebration', title: '庆祝与致敬', duration: 5000 }
];

export function AwardCeremonyPage() {
  const { nextPage, prevPage } = usePageNavigation();
  const [currentStage, setCurrentStage] = React.useState(0);
  const [showExplosion, setShowExplosion] = React.useState(false);
  const [selectedMember, setSelectedMember] = React.useState<string | null>(null);
  const [showBeams, setShowBeams] = React.useState(false);
  const [showMedal, setShowMedal] = React.useState(false);
  
  // 找到向云的信息
  const awardWinner = CREW_MEMBERS.find(member => member.name === '向云');
  
  React.useEffect(() => {
    const timers: NodeJS.Timeout[] = [];
    
    // 自动推进颁奖流程
    AWARD_STAGES.forEach((stage, index) => {
      const delay = AWARD_STAGES.slice(0, index).reduce((sum, s) => sum + s.duration, 1000);
      
      const timer = setTimeout(() => {
        setCurrentStage(index);
        
        // 特殊阶段处理
        if (stage.id === 'selection') {
          setTimeout(() => {
            setShowBeams(true);
            setSelectedMember('向云');
          }, 1000);
          
          setTimeout(() => {
            setShowExplosion(true);
          }, 3000);
        }
        
        if (stage.id === 'medal') {
          setTimeout(() => {
            setShowMedal(true);
          }, 500);
        }
      }, delay);
      
      timers.push(timer);
    });
    
    return () => timers.forEach(clearTimeout);
  }, []);
  
  const currentStageData = AWARD_STAGES[currentStage];
  
  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      {/* 深空背景 */}
      <StaticStarField className="absolute inset-0" density="high" />
      
      {/* 庆典环境光效 */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute inset-0 bg-gradient-radial from-yellow-500/20 via-transparent to-transparent"
          animate={{ opacity: currentStage >= 2 ? [0.3, 0.6, 0.3] : 0 }}
          transition={{ duration: 2, repeat: Infinity }}
        />
        <motion.div
          className="absolute inset-0 bg-gradient-radial from-purple-500/10 via-transparent to-transparent"
          animate={{ opacity: currentStage >= 3 ? [0.2, 0.5, 0.2] : 0 }}
          transition={{ duration: 3, repeat: Infinity, delay: 1 }}
        />
      </div>
      
      {/* 主要内容 */}
      <div className="relative z-10 container mx-auto px-8 py-16">
        {/* 颁奖标题 */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1.5 }}
        >
          <motion.div
            className="mb-8"
            animate={{ scale: [1, 1.05, 1] }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            <div className="text-yellow-400 font-mono text-xl mb-4">
              ★ 星光勋章授予仪式 ★
            </div>
            <div className="w-32 h-1 bg-gradient-to-r from-transparent via-yellow-400 to-transparent mx-auto" />
          </motion.div>
          
          <AnimatePresence mode="wait">
            {currentStage === 0 && (
              <motion.div
                key="announcement"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 1.2 }}
                transition={{ duration: 1 }}
              >
                <HologramText
                  variant="primary"
                  size="xl"
                  className="text-3xl md:text-5xl font-bold"
                >
                  本航程的'星光勋章'获得者是……
                </HologramText>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
        
        {/* 船员名单显示区域 */}
        <div className="relative max-w-6xl mx-auto mb-16">
          <AnimatePresence>
            {currentStage >= 1 && (
              <motion.div
                className="grid grid-cols-2 md:grid-cols-3 gap-6"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1 }}
              >
                {CREW_MEMBERS.map((member, index) => (
                  <motion.div
                    key={member.id}
                    className={cn(
                      'relative p-6 rounded-lg border-2 backdrop-blur-sm transition-all duration-1000',
                      selectedMember === member.name
                        ? 'border-yellow-400 bg-gradient-to-br from-yellow-900/40 to-purple-900/40'
                        : 'border-cyan-400/30 bg-gradient-to-br from-blue-900/20 to-purple-900/20'
                    )}
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.2, duration: 0.8 }}
                  >
                    {/* 选中光效 */}
                    {selectedMember === member.name && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-br from-yellow-400/20 to-purple-400/20 rounded-lg"
                        animate={{ opacity: [0.3, 0.8, 0.3] }}
                        transition={{ duration: 1, repeat: Infinity }}
                      />
                    )}
                    
                    <div className="relative z-10 text-center">
                      <motion.div
                        className={cn(
                          'text-2xl font-bold mb-2 transition-colors duration-1000',
                          selectedMember === member.name
                            ? 'text-yellow-300'
                            : 'text-cyan-300'
                        )}
                        animate={selectedMember === member.name ? {
                          textShadow: [
                            '0 0 10px #fbbf24',
                            '0 0 20px #fbbf24',
                            '0 0 10px #fbbf24'
                          ]
                        } : {}}
                        transition={{ duration: 1, repeat: Infinity }}
                      >
                        {member.name}
                      </motion.div>
                      
                      <div className={cn(
                        'text-sm font-mono transition-colors duration-1000',
                        selectedMember === member.name
                          ? 'text-yellow-400'
                          : 'text-cyan-400'
                      )}>
                        {member.position}
                      </div>
                      
                      {selectedMember === member.name && (
                        <motion.div
                          className="mt-3 text-xs text-yellow-200"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: 1 }}
                        >
                          ★ 获奖者 ★
                        </motion.div>
                      )}
                    </div>
                    
                    {/* 装饰角落 */}
                    <div className={cn(
                      'absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 transition-colors duration-1000',
                      selectedMember === member.name ? 'border-yellow-400' : 'border-cyan-400/50'
                    )} />
                    <div className={cn(
                      'absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 transition-colors duration-1000',
                      selectedMember === member.name ? 'border-yellow-400' : 'border-cyan-400/50'
                    )} />
                    <div className={cn(
                      'absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 transition-colors duration-1000',
                      selectedMember === member.name ? 'border-yellow-400' : 'border-cyan-400/50'
                    )} />
                    <div className={cn(
                      'absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 transition-colors duration-1000',
                      selectedMember === member.name ? 'border-yellow-400' : 'border-cyan-400/50'
                    )} />
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
          
          {/* 聚焦光束效果 */}
          <AnimatePresence>
            {showBeams && selectedMember && (
              <ConvergingBeams
                centerX={50}
                centerY={50}
                className="absolute inset-0 pointer-events-none"
              />
            )}
          </AnimatePresence>
          
          {/* 爆炸粒子效果 */}
          <AnimatePresence>
            {showExplosion && (
              <ExplosionParticles
                x={50}
                y={50}
                count={50}
                colors={['#fbbf24', '#f59e0b', '#d97706', '#92400e']}
                className="absolute inset-0 pointer-events-none"
              />
            )}
          </AnimatePresence>
        </div>
        
        {/* 星光勋章显示 */}
        <AnimatePresence>
          {showMedal && awardWinner && (
            <motion.div
              className="text-center mb-16"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 2, ease: 'easeOut' }}
            >
              {/* 勋章 */}
              <motion.div
                className="relative inline-block mb-8"
                animate={{ 
                  rotateY: [0, 360],
                  scale: [1, 1.1, 1]
                }}
                transition={{ 
                  rotateY: { duration: 4, repeat: Infinity, ease: 'linear' },
                  scale: { duration: 2, repeat: Infinity }
                }}
              >
                {/* 勋章主体 */}
                <div className="relative w-32 h-32 mx-auto">
                  {/* 外圈 */}
                  <motion.div
                    className="absolute inset-0 rounded-full border-4 border-yellow-400 bg-gradient-radial from-yellow-300/30 to-yellow-600/30"
                    animate={{ 
                      boxShadow: [
                        '0 0 20px #fbbf24',
                        '0 0 40px #fbbf24',
                        '0 0 20px #fbbf24'
                      ]
                    }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                  
                  {/* 内圈 */}
                  <motion.div
                    className="absolute inset-4 rounded-full border-2 border-yellow-300 bg-gradient-radial from-yellow-200/40 to-yellow-500/40"
                    animate={{ rotate: [0, -360] }}
                    transition={{ duration: 8, repeat: Infinity, ease: 'linear' }}
                  />
                  
                  {/* 中心星形 */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <motion.div
                      className="text-yellow-300 text-4xl"
                      animate={{ 
                        scale: [1, 1.2, 1],
                        textShadow: [
                          '0 0 10px #fbbf24',
                          '0 0 20px #fbbf24',
                          '0 0 10px #fbbf24'
                        ]
                      }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      ★
                    </motion.div>
                  </div>
                  
                  {/* 光芒效果 */}
                  {Array.from({ length: 8 }).map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-16 bg-gradient-to-t from-transparent via-yellow-400 to-transparent"
                      style={{
                        left: '50%',
                        top: '50%',
                        transformOrigin: '50% 50%',
                        transform: `translate(-50%, -50%) rotate(${i * 45}deg)`
                      }}
                      animate={{ 
                        opacity: [0.3, 1, 0.3],
                        scaleY: [0.8, 1.2, 0.8]
                      }}
                      transition={{ 
                        duration: 2, 
                        repeat: Infinity, 
                        delay: i * 0.2 
                      }}
                    />
                  ))}
                </div>
              </motion.div>
              
              {/* 获奖者信息 */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1, duration: 1 }}
              >
                <HologramText
                  variant="primary"
                  size="lg"
                  className="text-3xl md:text-4xl font-bold text-yellow-300 mb-6"
                >
                  {`${awardWinner.name} - 团队最闪亮的星！`}
                </HologramText>
                
                <motion.div
                  className="max-w-2xl mx-auto p-6 bg-gradient-to-br from-yellow-900/30 to-purple-900/30 border border-yellow-400/50 rounded-lg backdrop-blur-sm"
                  animate={{ 
                    boxShadow: [
                      '0 0 20px rgba(251, 191, 36, 0.3)',
                      '0 0 40px rgba(251, 191, 36, 0.5)',
                      '0 0 20px rgba(251, 191, 36, 0.3)'
                    ]
                  }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  <div className="text-yellow-200 text-lg leading-relaxed">
                    感谢你，在关键时刻给予的专业指导与支持！
                  </div>
                  <div className="text-yellow-400 text-sm font-mono mt-4">
                    —— 来自全体守护者号船员的致敬
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* 庆祝状态信息 */}
        {currentStage >= 4 && (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 2, duration: 1 }}
          >
            <div className="bg-gradient-to-br from-yellow-900/20 to-orange-900/20 border border-yellow-400/30 rounded-lg p-4 backdrop-blur-sm">
              <div className="text-yellow-400 font-mono text-sm mb-2">颁奖状态</div>
              <div className="text-yellow-300 text-lg">仪式完成</div>
              <div className="text-yellow-400 text-xs mt-1">勋章已授予</div>
            </div>
            
            <div className="bg-gradient-to-br from-purple-900/20 to-pink-900/20 border border-purple-400/30 rounded-lg p-4 backdrop-blur-sm">
              <div className="text-purple-400 font-mono text-sm mb-2">团队士气</div>
              <div className="text-purple-300 text-lg">空前高涨</div>
              <div className="text-purple-400 text-xs mt-1">庆祝模式: 激活</div>
            </div>
            
            <div className="bg-gradient-to-br from-green-900/20 to-blue-900/20 border border-green-400/30 rounded-lg p-4 backdrop-blur-sm">
              <div className="text-green-400 font-mono text-sm mb-2">任务进度</div>
              <div className="text-green-300 text-lg">即将完成</div>
              <div className="text-green-400 text-xs mt-1">准备返航</div>
            </div>
          </motion.div>
        )}
        
        {/* 导航按钮 */}
        <motion.div
          className="flex justify-between items-center mt-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 4, duration: 0.8 }}
        >
          <GlowButton
            variant="secondary"
            size="md"
            onClick={prevPage}
            className="px-6 py-3"
          >
            ← 舰桥广播
          </GlowButton>
          
          <div className="text-center">
            <div className="text-yellow-400 text-sm font-mono mb-2">
              颁奖进度
            </div>
            <div className="flex space-x-2">
              {AWARD_STAGES.map((_, index) => (
                <motion.div
                  key={index}
                  className={cn(
                    'w-3 h-3 rounded-full',
                    index <= currentStage
                      ? 'bg-yellow-400'
                      : 'bg-gray-600'
                  )}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: index * 0.2 }}
                />
              ))}
            </div>
          </div>
          
          <GlowButton
            variant="primary"
            size="md"
            onClick={nextPage}
            className="px-6 py-3"
          >
            任务完成 →
          </GlowButton>
        </motion.div>
      </div>
      
      {/* 庆祝粒子效果 */}
      <ParticleEffect
        count={currentStage >= 3 ? 80 : 40}
        colors={currentStage >= 3 ? ['#fbbf24', '#f59e0b', '#d97706', '#92400e'] : ['#06b6d4', '#3b82f6', '#8b5cf6']}
        speed={0.3}
        direction="up"
        className="absolute inset-0"
      />
    </div>
  );
}