import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { HologramText } from '../components/UI/HologramText';
import { GlowButton } from '../components/UI/GlowButton';
import { StaticStarField } from '../components/UI/StarField';
import { RadarScan } from '../components/Animations/RadarScan';
import { ParticleEffect } from '../components/Animations/ParticleEffect';
import { usePageNavigation } from '../contexts/AppContext';
import { cn } from '../lib/utils';

export function DiscoveryPage() {
  const { nextPage, prevPage } = usePageNavigation();
  const [scanPhase, setScanPhase] = React.useState<'scanning' | 'detected' | 'discovered'>('scanning');
  const [showNebula, setShowNebula] = React.useState(false);
  const [showCard, setShowCard] = React.useState(false);
  const [radarTargets, setRadarTargets] = React.useState<Array<{id: number, x: number, y: number, detected: boolean, label: string}>>([]);
  
  React.useEffect(() => {
    // 初始化雷达目标
    const targets = [
      { id: 1, x: 65, y: 35, detected: false, label: '目标A' },
      { id: 2, x: 45, y: 60, detected: false, label: '目标B' },
      { id: 3, x: 75, y: 70, detected: false, label: '目标C' },
    ];
    setRadarTargets(targets);
    
    // 扫描序列
    const scanTimer = setTimeout(() => {
      setScanPhase('detected');
      // 逐个检测目标
      targets.forEach((target, index) => {
        setTimeout(() => {
          setRadarTargets(prev => 
            prev.map(t => t.id === target.id ? { ...t, detected: true } : t)
          );
        }, index * 1000);
      });
      
      // 发现星云
      setTimeout(() => {
        setScanPhase('discovered');
        setShowNebula(true);
      }, 4000);
    }, 3000);
    
    return () => clearTimeout(scanTimer);
  }, []);
  
  const handleNebulaClick = () => {
    setShowCard(true);
  };
  
  const resetScan = () => {
    setScanPhase('scanning');
    setShowNebula(false);
    setShowCard(false);
    setRadarTargets(prev => prev.map(t => ({ ...t, detected: false })));
    
    setTimeout(() => {
      setScanPhase('detected');
      radarTargets.forEach((target, index) => {
        setTimeout(() => {
          setRadarTargets(prev => 
            prev.map(t => t.id === target.id ? { ...t, detected: true } : t)
          );
        }, index * 1000);
      });
      
      setTimeout(() => {
        setScanPhase('discovered');
        setShowNebula(true);
      }, 4000);
    }, 3000);
  };
  
  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      {/* 深空背景 */}
      <StaticStarField className="absolute inset-0" density="high" />
      
      {/* 深空星云背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-1/6 left-1/5 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/6 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl" />
        <div className="absolute top-2/3 left-1/3 w-64 h-64 bg-cyan-500/5 rounded-full blur-2xl" />
      </div>
      
      {/* 发现的星云 */}
      <AnimatePresence>
        {showNebula && (
          <motion.div
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            transition={{ duration: 2, ease: 'easeOut' }}
            onClick={handleNebulaClick}
          >
            {/* 星云核心 */}
            <motion.div
              className="relative w-64 h-64"
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
            >
              <div className="absolute inset-0 bg-gradient-radial from-yellow-400/30 via-orange-400/20 to-transparent rounded-full" />
              <div className="absolute inset-4 bg-gradient-radial from-yellow-300/40 via-orange-300/30 to-transparent rounded-full" />
              <div className="absolute inset-8 bg-gradient-radial from-yellow-200/50 via-orange-200/40 to-transparent rounded-full" />
              
              {/* 星云粒子 */}
              <ParticleEffect
                count={30}
                colors={['#fbbf24', '#f97316', '#fb923c', '#fdba74']}
                speed={0.2}
                direction="random"
                className="absolute inset-0"
              />
            </motion.div>
            
            {/* 发光效果 */}
            <motion.div
              className="absolute inset-0 bg-gradient-radial from-yellow-400/20 via-orange-400/10 to-transparent rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 3, repeat: Infinity }}
            />
            
            {/* 标签 */}
            <motion.div
              className="absolute -bottom-16 left-1/2 transform -translate-x-1/2 text-center"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1 }}
            >
              <div className="bg-yellow-400/20 border border-yellow-400/50 rounded-lg px-4 py-2 backdrop-blur-sm">
                <div className="text-yellow-400 font-mono text-sm">效率星云</div>
                <div className="text-yellow-300 text-xs">点击探索</div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* 主要内容 */}
      <div className="relative z-10 container mx-auto px-8 py-16">
        {/* 页面标题 */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          <motion.div
            className="flex items-center justify-center space-x-4 mb-4"
            animate={{ scale: [1, 1.02, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <motion.div
              className="w-3 h-3 rounded-full bg-green-400"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            />
            <span className="text-green-400 font-mono text-lg">◈ 深空扫描 ◈</span>
            <motion.div
              className="w-3 h-3 rounded-full bg-green-400"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 0.75 }}
            />
          </motion.div>
          
          <HologramText
            variant="primary"
            size="xl"
            className="text-center mb-8"
          >
            啊哈！发现"效率"星云！
          </HologramText>
          
          <motion.p
            className="text-cyan-400 text-lg font-mono"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            长程传感器检测到异常能量信号
          </motion.p>
        </motion.div>
        
        {/* 雷达扫描区域 */}
        <div className="relative max-w-4xl mx-auto mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* 雷达显示器 */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5, duration: 1 }}
            >
              <div className="bg-gradient-to-br from-green-900/20 to-blue-900/20 border border-green-400/30 rounded-lg p-6 backdrop-blur-sm">
                <div className="text-green-400 font-mono text-lg mb-4 text-center">
                  深空雷达 - 扫描模式
                </div>
                
                <div className="relative w-80 h-80 mx-auto">
                  <RadarScan
                    targets={radarTargets}
                    scanning={scanPhase === 'scanning'}
                    className="w-full h-full"
                  />
                </div>
                
                {/* 雷达状态 */}
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm font-mono">
                    <span className="text-green-400">扫描状态:</span>
                    <span className={cn(
                      scanPhase === 'scanning' && 'text-yellow-400',
                      scanPhase === 'detected' && 'text-orange-400',
                      scanPhase === 'discovered' && 'text-green-400'
                    )}>
                      {scanPhase === 'scanning' && '正在扫描...'}
                      {scanPhase === 'detected' && '检测到目标'}
                      {scanPhase === 'discovered' && '发现完成'}
                    </span>
                  </div>
                  
                  <div className="flex justify-between text-sm font-mono">
                    <span className="text-green-400">检测目标:</span>
                    <span className="text-cyan-400">
                      {radarTargets.filter(t => t.detected).length} / {radarTargets.length}
                    </span>
                  </div>
                  
                  <div className="flex justify-between text-sm font-mono">
                    <span className="text-green-400">信号强度:</span>
                    <span className="text-purple-400">
                      {scanPhase === 'discovered' ? '95%' : Math.floor(Math.random() * 30) + 60 + '%'}
                    </span>
                  </div>
                </div>
                
                {/* 重新扫描按钮 */}
                {scanPhase === 'discovered' && (
                  <motion.div
                    className="mt-4 text-center"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1 }}
                  >
                    <GlowButton
                      variant="secondary"
                      size="sm"
                      onClick={resetScan}
                      className="px-4 py-2"
                    >
                      重新扫描
                    </GlowButton>
                  </motion.div>
                )}
              </div>
            </motion.div>
            
            {/* 发现信息 */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1, duration: 1 }}
            >
              {/* 扫描日志 */}
              <div className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 border border-blue-400/30 rounded-lg p-6 backdrop-blur-sm mb-6">
                <div className="text-blue-400 font-mono text-lg mb-4">
                  扫描日志
                </div>
                
                <div className="space-y-2 text-sm font-mono max-h-48 overflow-y-auto">
                  <motion.div
                    className="text-gray-400"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5 }}
                  >
                    [00:01] 初始化深空扫描协议...
                  </motion.div>
                  
                  <motion.div
                    className="text-gray-400"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1 }}
                  >
                    [00:03] 扫描范围: 10光年半径
                  </motion.div>
                  
                  {scanPhase !== 'scanning' && (
                    <motion.div
                      className="text-yellow-400"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      [00:05] 检测到异常能量信号
                    </motion.div>
                  )}
                  
                  {radarTargets.filter(t => t.detected).length > 0 && (
                    <motion.div
                      className="text-orange-400"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5 }}
                    >
                      [00:07] 发现 {radarTargets.filter(t => t.detected).length} 个目标
                    </motion.div>
                  )}
                  
                  {scanPhase === 'discovered' && (
                    <motion.div
                      className="text-green-400"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.8 }}
                    >
                      [00:10] 确认发现：效率星云！
                    </motion.div>
                  )}
                </div>
              </div>
              
              {/* 发现提示 */}
              {scanPhase === 'discovered' && !showCard && (
                <motion.div
                  className="bg-gradient-to-br from-yellow-900/20 to-orange-900/20 border border-yellow-400/30 rounded-lg p-6 backdrop-blur-sm"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.5 }}
                >
                  <div className="text-yellow-400 font-mono text-lg mb-2">
                    新发现！
                  </div>
                  <div className="text-yellow-300 text-sm mb-4">
                    检测到一片未知的星云，散发着独特的能量信号。
                    点击星云以获取详细分析报告。
                  </div>
                  
                  <motion.div
                    className="flex items-center space-x-2"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <span className="text-yellow-400">→</span>
                    <span className="text-yellow-300 text-sm">点击中央星云</span>
                  </motion.div>
                </motion.div>
              )}
            </motion.div>
          </div>
        </div>
        
        {/* 发现卡片 */}
        <AnimatePresence>
          {showCard && (
            <motion.div
              className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setShowCard(false)}
            >
              <motion.div
                className="bg-gradient-to-br from-yellow-900/40 to-orange-900/40 border border-yellow-400/50 rounded-lg p-8 max-w-2xl mx-4 backdrop-blur-sm"
                initial={{ opacity: 0, scale: 0.8, y: 50 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: 50 }}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="text-center mb-6">
                  <HologramText
                    variant="accent"
                    size="lg"
                    className="text-3xl font-bold mb-4"
                  >
                    工作方法的新发现
                  </HologramText>
                  
                  <div className="w-16 h-1 bg-gradient-to-r from-transparent via-yellow-400 to-transparent mx-auto mb-6" />
                </div>
                
                <div className="text-yellow-100 text-lg leading-relaxed mb-8">
                  不必一味追求自动化和最新技术栈，在开发仅一人的情况下，
                  <span className="text-yellow-400 font-semibold">优先满足核心需求才是王道</span>。
                  告别完美主义！
                </div>
                
                <div className="bg-yellow-400/10 border border-yellow-400/30 rounded-lg p-4 mb-6">
                  <div className="text-yellow-400 font-mono text-sm mb-2">分析报告</div>
                  <div className="text-yellow-200 text-sm">
                    这片星云蕴含着关于效率和实用主义的智慧。在资源有限的情况下，
                    专注于解决核心问题往往比追求技术完美更有价值。
                  </div>
                </div>
                
                <div className="text-center">
                  <GlowButton
                    variant="accent"
                    size="md"
                    onClick={() => setShowCard(false)}
                    className="px-6 py-3"
                  >
                    收录到航行日志
                  </GlowButton>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* 导航按钮 */}
        <motion.div
          className="flex justify-between items-center mt-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2, duration: 0.8 }}
        >
          <GlowButton
            variant="secondary"
            size="md"
            onClick={prevPage}
            className="px-6 py-3"
          >
            ← 时空通讯
          </GlowButton>
          
          <div className="text-center">
            <div className="text-green-400 text-sm font-mono mb-2">
              扫描进度
            </div>
            <div className="flex space-x-2">
              <motion.div
                className={cn(
                  'w-3 h-3 rounded-full',
                  scanPhase !== 'scanning' ? 'bg-green-400' : 'bg-green-400/30'
                )}
                animate={scanPhase === 'scanning' ? { scale: [1, 1.2, 1] } : {}}
                transition={{ duration: 1, repeat: Infinity }}
              />
              <motion.div
                className={cn(
                  'w-3 h-3 rounded-full',
                  scanPhase === 'discovered' ? 'bg-yellow-400' : 'bg-yellow-400/30'
                )}
                animate={scanPhase === 'discovered' ? { scale: [1, 1.2, 1] } : {}}
                transition={{ duration: 1, repeat: Infinity }}
              />
            </div>
          </div>
          
          <GlowButton
            variant="primary"
            size="md"
            onClick={nextPage}
            className="px-6 py-3"
          >
            颁奖仪式 →
          </GlowButton>
        </motion.div>
      </div>
      
      {/* 深空粒子效果 */}
      <ParticleEffect
        count={40}
        colors={['#10b981', '#06b6d4', '#3b82f6', '#8b5cf6']}
        speed={0.3}
        direction="random"
        className="absolute inset-0"
      />
    </div>
  );
}