import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { HologramText } from '../components/UI/HologramText';
import { GlowButton } from '../components/UI/GlowButton';
import { StaticStarField } from '../components/UI/StarField';
import { ParticleEffect } from '../components/Animations/ParticleEffect';
import { Starship } from '../components/Animations/StarshipJump';
import { usePageNavigation } from '../contexts/AppContext';
import { CHALLENGES } from '../lib/constants';
import { cn } from '../lib/utils';

export function ChallengePage() {
  const { nextPage, prevPage } = usePageNavigation();
  const [selectedChallenge, setSelectedChallenge] = React.useState<number | null>(null);
  const [showAsteroids, setShowAsteroids] = React.useState(false);
  const [showShipEvasion, setShowShipEvasion] = React.useState(false);
  
  React.useEffect(() => {
    const timer = setTimeout(() => setShowAsteroids(true), 1000);
    return () => clearTimeout(timer);
  }, []);
  
  const handleAsteroidClick = (index: number) => {
    setSelectedChallenge(index);
    setShowShipEvasion(true);
    
    // 重置规避动画
    setTimeout(() => setShowShipEvasion(false), 3000);
  };
  
  const asteroidPositions = [
    { top: '20%', left: '15%', size: 'large' },
    { top: '45%', left: '60%', size: 'medium' },
    { top: '70%', left: '30%', size: 'large' },
    { top: '25%', left: '75%', size: 'small' },
    { top: '60%', left: '80%', size: 'medium' },
    { top: '80%', left: '10%', size: 'small' }
  ];
  
  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      {/* 深空背景 */}
      <StaticStarField className="absolute inset-0" density="medium" />
      
      {/* 危险区域效果 */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute inset-0 bg-red-900/5"
          animate={{ opacity: [0.05, 0.15, 0.05] }}
          transition={{ duration: 3, repeat: Infinity }}
        />
        
        {/* 警告网格 */}
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full" style={{
            backgroundImage: `
              linear-gradient(rgba(239, 68, 68, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(239, 68, 68, 0.3) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px'
          }} />
        </div>
      </div>
      
      {/* 危险粒子效果 */}
      <ParticleEffect
        count={60}
        colors={['#ef4444', '#f97316', '#eab308']}
        speed={1.2}
        direction="random"
        className="absolute inset-0"
      />
      
      {/* 主要内容 */}
      <div className="relative z-10 container mx-auto px-8 py-16">
        {/* 警报标题 */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          <motion.div
            className="flex items-center justify-center space-x-4 mb-4"
            animate={{ scale: [1, 1.05, 1] }}
            transition={{ duration: 1, repeat: Infinity }}
          >
            <motion.div
              className="w-4 h-4 rounded-full bg-red-500"
              animate={{ opacity: [1, 0.3, 1] }}
              transition={{ duration: 0.5, repeat: Infinity }}
            />
            <span className="text-red-400 font-mono text-lg">⚠ 危险警报 ⚠</span>
            <motion.div
              className="w-4 h-4 rounded-full bg-red-500"
              animate={{ opacity: [1, 0.3, 1] }}
              transition={{ duration: 0.5, repeat: Infinity, delay: 0.25 }}
            />
          </motion.div>
          
          <HologramText
            variant="accent"
            size="xl"
            glitch={true}
            className="text-3xl md:text-5xl font-bold mb-8"
          >
            警报！穿越小行星带！
          </HologramText>
          
          <motion.p
            className="text-orange-400 text-lg font-mono"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            检测到多个威胁目标 - 启动规避程序
          </motion.p>
        </motion.div>
        
        {/* 小行星带区域 */}
        <div className="relative max-w-6xl mx-auto h-96 md:h-[500px] mb-16">
          {/* 小行星 */}
          {showAsteroids && asteroidPositions.map((pos, index) => {
            const isChallenge = index < CHALLENGES.length;
            const challenge = isChallenge ? CHALLENGES[index] : null;
            
            return (
              <motion.div
                key={index}
                className={cn(
                  'absolute cursor-pointer group',
                  isChallenge && 'z-10'
                )}
                style={{ top: pos.top, left: pos.left }}
                initial={{ scale: 0, rotate: 0 }}
                animate={{ 
                  scale: 1, 
                  rotate: 360,
                  x: [0, 10, -10, 0],
                  y: [0, -5, 5, 0]
                }}
                transition={{ 
                  scale: { delay: index * 0.3, duration: 0.8 },
                  rotate: { duration: 20, repeat: Infinity, ease: 'linear' },
                  x: { duration: 4, repeat: Infinity, delay: index * 0.5 },
                  y: { duration: 3, repeat: Infinity, delay: index * 0.3 }
                }}
                whileHover={{ scale: 1.1 }}
                onClick={() => isChallenge && handleAsteroidClick(index)}
              >
                {/* 小行星主体 */}
                <div className={cn(
                  'relative rounded-full border-2 backdrop-blur-sm',
                  pos.size === 'large' ? 'w-16 h-16' : pos.size === 'medium' ? 'w-12 h-12' : 'w-8 h-8',
                  isChallenge 
                    ? 'bg-red-500/30 border-red-400 shadow-lg shadow-red-400/50'
                    : 'bg-gray-500/30 border-gray-400 shadow-lg shadow-gray-400/30'
                )}>
                  {/* 小行星纹理 */}
                  <div className="absolute inset-1 rounded-full bg-gradient-to-br from-transparent via-white/10 to-transparent" />
                  
                  {/* 危险标识 */}
                  {isChallenge && (
                    <motion.div
                      className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity }}
                    >
                      <span className="text-white text-xs font-bold">!</span>
                    </motion.div>
                  )}
                  
                  {/* 威胁光环 */}
                  {isChallenge && (
                    <motion.div
                      className="absolute inset-0 rounded-full border border-red-400/50"
                      animate={{ scale: [1, 1.8, 1], opacity: [0.8, 0, 0.8] }}
                      transition={{ duration: 2, repeat: Infinity, delay: index * 0.3 }}
                    />
                  )}
                </div>
                
                {/* 挑战标签 */}
                {isChallenge && challenge && (
                  <motion.div
                    className="absolute top-full mt-2 left-1/2 transform -translate-x-1/2 whitespace-nowrap"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1 + index * 0.3 }}
                  >
                    <div className="bg-red-900/80 border border-red-400/50 rounded px-3 py-1 text-sm text-red-200 font-mono">
                      {challenge.title}
                    </div>
                  </motion.div>
                )}
                
                {/* 悬停提示 */}
                {isChallenge && (
                  <motion.div
                    className="absolute -top-16 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none"
                  >
                    <div className="bg-red-900/90 border border-red-400/50 rounded px-3 py-2 text-sm text-red-200 whitespace-nowrap">
                      点击查看解决方案
                    </div>
                  </motion.div>
                )}
              </motion.div>
            );
          })}
          
          {/* 星舰规避动画 */}
          <AnimatePresence>
            {showShipEvasion && (
              <motion.div
                className="absolute top-1/2 left-0 transform -translate-y-1/2"
                initial={{ x: -100, opacity: 0 }}
                animate={{ 
                  x: [0, 200, 400, 600, 800],
                  y: [0, -50, 50, -30, 0],
                  opacity: [0, 1, 1, 1, 0]
                }}
                exit={{ opacity: 0 }}
                transition={{ duration: 3, ease: 'easeInOut' }}
              >
                <Starship size="sm" animated />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        
        {/* 解决方案显示 */}
        <AnimatePresence>
          {selectedChallenge !== null && (
            <motion.div
              className="max-w-4xl mx-auto mb-16"
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -50, scale: 0.9 }}
              transition={{ duration: 0.5 }}
            >
              <div className="bg-gradient-to-br from-green-900/20 to-blue-900/20 border border-green-400/30 rounded-lg p-8 backdrop-blur-sm">
                <div className="flex items-center space-x-4 mb-6">
                  <motion.div
                    className="w-4 h-4 rounded-full bg-green-400"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                  <h3 className="text-2xl font-bold text-green-400">
                    解决方案已激活
                  </h3>
                </div>
                
                <div className="space-y-4">
                  <div className="text-red-400 font-mono text-lg font-bold">
                    威胁：{CHALLENGES[selectedChallenge].title}
                  </div>
                  
                  <div className="text-orange-300 text-base">
                    问题描述：{CHALLENGES[selectedChallenge].description}
                  </div>
                  
                  <div className="border-t border-green-400/30 pt-4">
                    <div className="text-green-400 font-mono text-sm mb-2">
                      ✓ 解决方案执行中...
                    </div>
                    <div className="text-gray-200 text-lg leading-relaxed">
                      {CHALLENGES[selectedChallenge].solution}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4 mt-6">
                    <div className="text-sm text-gray-400">
                      威胁等级：
                    </div>
                    <div className="flex space-x-1">
                      {Array.from({ length: 5 }, (_, i) => (
                        <motion.div
                          key={i}
                          className={cn(
                            'w-2 h-2 rounded-full',
                            i < CHALLENGES[selectedChallenge].difficulty
                              ? 'bg-red-400'
                              : 'bg-gray-600'
                          )}
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: i * 0.1 }}
                        />
                      ))}
                    </div>
                    
                    <div className="ml-8 text-sm text-gray-400">
                      解决状态：
                    </div>
                    <motion.div
                      className="px-3 py-1 bg-green-400/20 border border-green-400/50 rounded text-green-400 text-sm"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.5 }}
                    >
                      已解决
                    </motion.div>
                  </div>
                </div>
                
                {/* 关闭按钮 */}
                <motion.button
                  className="mt-6 px-6 py-2 bg-green-600/20 border border-green-400/50 rounded text-green-400 hover:bg-green-600/30 transition-colors"
                  onClick={() => setSelectedChallenge(null)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  关闭报告
                </motion.button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* 导航按钮 */}
        <motion.div
          className="flex justify-between items-center mt-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 3, duration: 0.8 }}
        >
          <GlowButton
            variant="secondary"
            size="md"
            onClick={prevPage}
            className="px-6 py-3"
          >
            ← 航行日志
          </GlowButton>
          
          <div className="text-center">
            <div className="text-green-400 text-sm font-mono mb-2">
              威胁评估完成
            </div>
            <div className="flex space-x-2">
              {CHALLENGES.map((_, index) => (
                <motion.div
                  key={index}
                  className={cn(
                    'w-3 h-3 rounded-full border-2',
                    selectedChallenge === index
                      ? 'bg-green-400 border-green-400'
                      : 'bg-red-400/20 border-red-400'
                  )}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 1.5 + index * 0.2 }}
                />
              ))}
            </div>
          </div>
          
          <GlowButton
            variant="primary"
            size="md"
            onClick={nextPage}
            className="px-6 py-3"
          >
            接收通讯 →
          </GlowButton>
        </motion.div>
      </div>
      
      {/* 危险扫描效果 */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: [0, 0.3, 0] }}
        transition={{
          duration: 2,
          repeat: Infinity,
          repeatDelay: 3
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-red-500/10 via-transparent to-red-500/10" />
      </motion.div>
    </div>
  );
}