import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { HologramText, TypewriterText } from '../components/UI/HologramText';
import { GlowButton } from '../components/UI/GlowButton';
import { StaticStarField, Nebula } from '../components/UI/StarField';
import { CircularProgress } from '../components/UI/ProgressBar';
import { Starship } from '../components/Animations/StarshipJump';
import { ParticleEffect } from '../components/Animations/ParticleEffect';
import { usePageNavigation } from '../contexts/AppContext';
import { cn } from '../lib/utils';

interface MissionMetric {
  id: string;
  label: string;
  value: number;
  maxValue: number;
  unit: string;
  color: string;
}

const MISSION_METRICS: MissionMetric[] = [
  { id: 'completion', label: '任务完成度', value: 4.5, maxValue: 5, unit: '', color: '#10b981' },
  { id: 'teamwork', label: '团队协作', value: 98, maxValue: 100, unit: '%', color: '#3b82f6' },
  { id: 'innovation', label: '创新指数', value: 92, maxValue: 100, unit: '%', color: '#8b5cf6' },
  { id: 'efficiency', label: '执行效率', value: 89, maxValue: 100, unit: '%', color: '#f59e0b' }
];

interface CompletionStage {
  id: string;
  title: string;
  duration: number;
}

const COMPLETION_STAGES: CompletionStage[] = [
  { id: 'metrics', title: '显示任务指标', duration: 3000 },
  { id: 'starship', title: '星舰准备返航', duration: 2000 },
  { id: 'docking', title: '驶入空间站', duration: 4000 },
  { id: 'farewell', title: '告别致辞', duration: 3000 }
];

export function CompletionPage() {
  const { prevPage, goToPage } = usePageNavigation();
  const [currentStage, setCurrentStage] = React.useState(0);
  const [showMetrics, setShowMetrics] = React.useState(false);
  const [showStarship, setShowStarship] = React.useState(false);
  const [showStation, setShowStation] = React.useState(false);
  const [showFarewell, setShowFarewell] = React.useState(false);
  const [isDocked, setIsDocked] = React.useState(false);
  
  React.useEffect(() => {
    const timers: NodeJS.Timeout[] = [];
    
    // 自动推进完成流程
    COMPLETION_STAGES.forEach((stage, index) => {
      const delay = COMPLETION_STAGES.slice(0, index).reduce((sum, s) => sum + s.duration, 1000);
      
      const timer = setTimeout(() => {
        setCurrentStage(index);
        
        // 特殊阶段处理
        if (stage.id === 'metrics') {
          setShowMetrics(true);
        }
        
        if (stage.id === 'starship') {
          setShowStarship(true);
        }
        
        if (stage.id === 'docking') {
          setShowStation(true);
          setTimeout(() => setIsDocked(true), 2000);
        }
        
        if (stage.id === 'farewell') {
          setShowFarewell(true);
        }
      }, delay);
      
      timers.push(timer);
    });
    
    return () => timers.forEach(clearTimeout);
  }, []);
  
  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      {/* 深空背景 */}
      <StaticStarField className="absolute inset-0" density="high" />
      
      {/* 星云背景 */}
      <Nebula 
        colors={['#1e40af', '#7c3aed', '#059669']}
        className="absolute inset-0 opacity-30"
      />
      
      {/* 空间站 */}
      <AnimatePresence>
        {showStation && (
          <motion.div
            className="absolute right-0 top-1/2 transform -translate-y-1/2"
            initial={{ x: 200, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 2, ease: 'easeOut' }}
          >
            {/* 空间站主体 */}
            <div className="relative w-64 h-64">
              {/* 中央核心 */}
              <motion.div
                className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 rounded-full bg-gradient-radial from-blue-400/40 to-blue-600/40 border-2 border-blue-400"
                animate={{ 
                  boxShadow: [
                    '0 0 20px #3b82f6',
                    '0 0 40px #3b82f6',
                    '0 0 20px #3b82f6'
                  ]
                }}
                transition={{ duration: 2, repeat: Infinity }}
              />
              
              {/* 旋转环 */}
              <motion.div
                className="absolute inset-0 border-2 border-cyan-400/50 rounded-full"
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
              >
                {/* 对接港 */}
                {Array.from({ length: 6 }).map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-4 h-8 bg-gradient-to-b from-green-400/60 to-blue-400/60 rounded"
                    style={{
                      left: '50%',
                      top: '50%',
                      transformOrigin: '50% 128px',
                      transform: `translate(-50%, -50%) rotate(${i * 60}deg)`
                    }}
                    animate={{ opacity: [0.5, 1, 0.5] }}
                    transition={{ duration: 2, repeat: Infinity, delay: i * 0.3 }}
                  />
                ))}
              </motion.div>
              
              {/* 外层结构 */}
              <motion.div
                className="absolute inset-4 border border-purple-400/30 rounded-full"
                animate={{ rotate: [0, -360] }}
                transition={{ duration: 30, repeat: Infinity, ease: 'linear' }}
              >
                {Array.from({ length: 8 }).map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-2 h-2 bg-purple-400 rounded-full"
                    style={{
                      left: '50%',
                      top: '50%',
                      transformOrigin: '50% 96px',
                      transform: `translate(-50%, -50%) rotate(${i * 45}deg)`
                    }}
                  />
                ))}
              </motion.div>
              
              {/* 对接指示灯 */}
              {isDocked && (
                <motion.div
                  className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 rounded-full bg-green-400"
                  initial={{ scale: 0 }}
                  animate={{ 
                    scale: [0, 1.2, 1],
                    boxShadow: [
                      '0 0 10px #10b981',
                      '0 0 20px #10b981',
                      '0 0 10px #10b981'
                    ]
                  }}
                  transition={{ duration: 1, repeat: Infinity }}
                />
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* 星舰 */}
      <AnimatePresence>
        {showStarship && (
          <motion.div
            className="absolute left-1/4 top-1/2 transform -translate-y-1/2"
            initial={{ x: -200, opacity: 0 }}
            animate={{ 
              x: isDocked ? 100 : 0, 
              opacity: 1,
              scale: isDocked ? 0.8 : 1
            }}
            transition={{ 
              x: { duration: isDocked ? 3 : 1.5, ease: 'easeInOut' },
              opacity: { duration: 1 },
              scale: { duration: isDocked ? 2 : 0 }
            }}
          >
            <Starship 
              size="lg" 
              className={cn(
                'transform',
                isDocked ? 'rotate-12' : 'rotate-0'
              )}
            />
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* 主要内容 */}
      <div className="relative z-10 container mx-auto px-8 py-16">
        {/* 页面标题 */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          <motion.div
            className="flex items-center justify-center space-x-4 mb-6"
            animate={{ scale: [1, 1.02, 1] }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            <motion.div
              className="w-4 h-4 rounded-full bg-green-400"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            />
            <span className="text-green-400 font-mono text-xl">◈ 任务状态：完成 ◈</span>
            <motion.div
              className="w-4 h-4 rounded-full bg-green-400"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 0.75 }}
            />
          </motion.div>
          
          <HologramText
            variant="primary"
            size="xl"
            className="text-4xl md:text-6xl font-bold mb-4"
          >
            任务完成，成功返航！
          </HologramText>
          
          <motion.p
            className="text-cyan-400 text-lg font-mono"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            守护者号 - 航行任务总结报告
          </motion.p>
        </motion.div>
        
        {/* 任务指标仪表盘 */}
        <AnimatePresence>
          {showMetrics && (
            <motion.div
              className="max-w-6xl mx-auto mb-16"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1.5, ease: 'easeOut' }}
            >
              {/* 主要完成度指标 */}
              <div className="text-center mb-12">
                <motion.div
                  className="inline-block relative"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.5, duration: 1, ease: 'easeOut' }}
                >
                  <CircularProgress
                    progress={(MISSION_METRICS[0].value / MISSION_METRICS[0].maxValue) * 100}
                    size={200}
                    strokeWidth={8}
                    variant="accent"
                    className="mb-4"
                  />
                  
                  <motion.div
                    className="absolute inset-0 flex flex-col items-center justify-center"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1.5, duration: 0.8 }}
                  >
                    <div className="text-4xl font-bold text-green-300 mb-2">
                      {MISSION_METRICS[0].value} / {MISSION_METRICS[0].maxValue}
                    </div>
                    <div className="text-green-400 font-mono text-sm">
                      {MISSION_METRICS[0].label}
                    </div>
                  </motion.div>
                </motion.div>
              </div>
              
              {/* 详细指标网格 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {MISSION_METRICS.slice(1).map((metric, index) => (
                  <motion.div
                    key={metric.id}
                    className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 border border-cyan-400/30 rounded-lg p-6 backdrop-blur-sm"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1 + index * 0.3, duration: 0.8 }}
                  >
                    <div className="text-center">
                      <div className="mb-4">
                        <CircularProgress
                          progress={(metric.value / metric.maxValue) * 100}
                          size={80}
                          strokeWidth={6}
                          variant="primary"
                        />
                      </div>
                      
                      <div className="text-xl font-bold mb-2" style={{ color: metric.color }}>
                        {metric.value}{metric.unit}
                      </div>
                      
                      <div className="text-cyan-400 font-mono text-sm">
                        {metric.label}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* 对接状态信息 */}
        {isDocked && (
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            <div className="max-w-2xl mx-auto p-6 bg-gradient-to-br from-green-900/30 to-blue-900/30 border border-green-400/50 rounded-lg backdrop-blur-sm">
              <motion.div
                className="text-green-400 font-mono text-lg mb-2"
                animate={{ opacity: [0.7, 1, 0.7] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                ✓ 对接完成
              </motion.div>
              
              <div className="text-green-300 text-base">
                守护者号已成功停靠至星际空间站
              </div>
              
              <div className="text-green-400 text-sm font-mono mt-2">
                对接时间: {new Date().toLocaleTimeString()}
              </div>
            </div>
          </motion.div>
        )}
        
        {/* 告别致辞 */}
        <AnimatePresence>
          {showFarewell && (
            <motion.div
              className="text-center mb-16"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1.5 }}
            >
              <div className="max-w-4xl mx-auto p-8 bg-gradient-to-br from-purple-900/30 to-blue-900/30 border border-purple-400/50 rounded-lg backdrop-blur-sm">
                <motion.div
                  className="mb-6"
                  animate={{ scale: [1, 1.02, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <div className="text-purple-400 font-mono text-lg mb-4">
                    ★ 舰长致辞 ★
                  </div>
                  <div className="w-24 h-1 bg-gradient-to-r from-transparent via-purple-400 to-transparent mx-auto" />
                </motion.div>
                
                <TypewriterText
                  text="感谢每一位船员的付出，期待下一次起航！"
                  speed={50}
                  className="text-2xl md:text-3xl text-purple-200 leading-relaxed font-light mb-6"
                />
                
                <motion.div
                  className="text-purple-300 font-mono text-sm"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 3, duration: 1 }}
                >
                  —— 守护者号全体船员
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* 任务统计 */}
        {currentStage >= 3 && (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-16"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 2, duration: 1 }}
          >
            <div className="bg-gradient-to-br from-blue-900/20 to-cyan-900/20 border border-blue-400/30 rounded-lg p-4 backdrop-blur-sm">
              <div className="text-blue-400 font-mono text-sm mb-2">航行时长</div>
              <div className="text-blue-300 text-lg">9 页面</div>
              <div className="text-blue-400 text-xs mt-1">完整体验</div>
            </div>
            
            <div className="bg-gradient-to-br from-green-900/20 to-emerald-900/20 border border-green-400/30 rounded-lg p-4 backdrop-blur-sm">
              <div className="text-green-400 font-mono text-sm mb-2">船员数量</div>
              <div className="text-green-300 text-lg">6 名</div>
              <div className="text-green-400 text-xs mt-1">全员安全</div>
            </div>
            
            <div className="bg-gradient-to-br from-purple-900/20 to-violet-900/20 border border-purple-400/30 rounded-lg p-4 backdrop-blur-sm">
              <div className="text-purple-400 font-mono text-sm mb-2">挑战解决</div>
              <div className="text-purple-300 text-lg">3 项</div>
              <div className="text-purple-400 text-xs mt-1">100% 成功</div>
            </div>
            
            <div className="bg-gradient-to-br from-yellow-900/20 to-orange-900/20 border border-yellow-400/30 rounded-lg p-4 backdrop-blur-sm">
              <div className="text-yellow-400 font-mono text-sm mb-2">勋章获得</div>
              <div className="text-yellow-300 text-lg">1 枚</div>
              <div className="text-yellow-400 text-xs mt-1">星光勋章</div>
            </div>
          </motion.div>
        )}
        
        {/* 导航按钮 */}
        <motion.div
          className="flex justify-between items-center mt-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 4, duration: 0.8 }}
        >
          <GlowButton
            variant="secondary"
            size="md"
            onClick={prevPage}
            className="px-6 py-3"
          >
            ← 星光勋章
          </GlowButton>
          
          <div className="text-center">
            <div className="text-cyan-400 text-sm font-mono mb-2">
              任务进度
            </div>
            <div className="flex space-x-2">
              {COMPLETION_STAGES.map((_, index) => (
                <motion.div
                  key={index}
                  className={cn(
                    'w-3 h-3 rounded-full',
                    index <= currentStage
                      ? 'bg-green-400'
                      : 'bg-gray-600'
                  )}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: index * 0.2 }}
                />
              ))}
            </div>
          </div>
          
          <GlowButton
            variant="accent"
            size="md"
            onClick={() => goToPage(0)}
            className="px-6 py-3"
          >
            重新开始 ↻
          </GlowButton>
        </motion.div>
      </div>
      
      {/* 庆祝粒子效果 */}
      <ParticleEffect
        count={isDocked ? 100 : 50}
        colors={isDocked ? ['#10b981', '#059669', '#047857', '#065f46'] : ['#06b6d4', '#0891b2', '#0e7490']}
        speed={0.4}
        direction="up"
        className="absolute inset-0"
      />
    </div>
  );
}