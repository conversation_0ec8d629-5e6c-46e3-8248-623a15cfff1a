import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { HologramText, TypewriterText } from '../components/UI/HologramText';
import { GlowButton } from '../components/UI/GlowButton';
import { StaticStarField } from '../components/UI/StarField';
import { ParticleEffect } from '../components/Animations/ParticleEffect';
import { LightBeam } from '../components/Animations/LightBeam';
import { usePageNavigation } from '../contexts/AppContext';
import { WISDOM_ADVICES } from '../lib/constants';
import { cn } from '../lib/utils';

export function WisdomPage() {
  const { nextPage, prevPage } = usePageNavigation();
  const [currentAdvice, setCurrentAdvice] = React.useState(0);
  const [showHologram, setShowHologram] = React.useState(false);
  const [isPlaying, setIsPlaying] = React.useState(false);
  const [showBeams, setShowBeams] = React.useState(false);
  
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setShowHologram(true);
      setShowBeams(true);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);
  
  const startTransmission = () => {
    setIsPlaying(true);
    setCurrentAdvice(0);
  };
  
  const nextAdvice = () => {
    if (currentAdvice < WISDOM_ADVICES.length - 1) {
      setCurrentAdvice(currentAdvice + 1);
    } else {
      setIsPlaying(false);
    }
  };
  
  const prevAdvice = () => {
    if (currentAdvice > 0) {
      setCurrentAdvice(currentAdvice - 1);
    }
  };
  
  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      {/* 深空背景 */}
      <StaticStarField className="absolute inset-0" density="high" />
      
      {/* 神秘星云效果 */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/3 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 right-1/4 w-48 h-48 bg-cyan-500/10 rounded-full blur-2xl" />
      </div>
      
      {/* 光束效果 */}
      {showBeams && (
        <>
          <LightBeam
            startX={10}
            startY={20}
            endX={50}
            endY={50}
            color="#8b5cf6"
            duration={3}
            intensity={0.6}
          />
          <LightBeam
            startX={90}
            startY={30}
            endX={50}
            endY={50}
            color="#06b6d4"
            duration={3}
            delay={0.5}
            intensity={0.6}
          />
          <LightBeam
            startX={50}
            startY={90}
            endX={50}
            endY={50}
            color="#3b82f6"
            duration={3}
            delay={1}
            intensity={0.6}
          />
        </>
      )}
      
      {/* 神秘粒子效果 */}
      <ParticleEffect
        count={50}
        colors={['#8b5cf6', '#06b6d4', '#3b82f6', '#a855f7']}
        speed={0.4}
        direction="random"
        className="absolute inset-0"
      />
      
      {/* 主要内容 */}
      <div className="relative z-10 container mx-auto px-8 py-16">
        {/* 页面标题 */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          <motion.div
            className="flex items-center justify-center space-x-4 mb-4"
            animate={{ scale: [1, 1.02, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <motion.div
              className="w-3 h-3 rounded-full bg-purple-400"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            />
            <span className="text-purple-400 font-mono text-lg">◈ 时空通讯 ◈</span>
            <motion.div
              className="w-3 h-3 rounded-full bg-purple-400"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 0.75 }}
            />
          </motion.div>
          
          <HologramText
            variant="primary"
            size="xl"
            className="text-4xl md:text-6xl font-bold mb-4"
          >
            捕获来自过去的通讯
          </HologramText>
          
          <motion.p
            className="text-cyan-400 text-lg font-mono"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            时间坐标：过去的自己 → 现在的我们
          </motion.p>
        </motion.div>
        
        {/* 全息投影区域 */}
        <div className="relative max-w-4xl mx-auto mb-16">
          {/* 全息投影台 */}
          <motion.div
            className="relative h-96 md:h-[500px] flex items-center justify-center"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1, duration: 1 }}
          >
            {/* 投影台基座 */}
            <div className="absolute bottom-0 w-64 h-8 bg-gradient-to-r from-transparent via-blue-400/30 to-transparent rounded-full" />
            <div className="absolute bottom-2 w-48 h-4 bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent rounded-full" />
            
            {/* 全息网格 */}
            {showHologram && (
              <motion.div
                className="absolute inset-0 opacity-20"
                initial={{ opacity: 0 }}
                animate={{ opacity: 0.2 }}
                transition={{ duration: 2 }}
              >
                <div className="w-full h-full" style={{
                  backgroundImage: `
                    linear-gradient(rgba(139, 92, 246, 0.4) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(139, 92, 246, 0.4) 1px, transparent 1px)
                  `,
                  backgroundSize: '30px 30px'
                }} />
              </motion.div>
            )}
            
            {/* 全息投影内容 */}
            <AnimatePresence mode="wait">
              {showHologram && !isPlaying && (
                <motion.div
                  className="text-center"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -50 }}
                  transition={{ duration: 0.8 }}
                >
                  <motion.div
                    className="mb-8"
                    animate={{ rotateY: [0, 360] }}
                    transition={{ duration: 10, repeat: Infinity, ease: 'linear' }}
                  >
                    <div className="w-32 h-32 mx-auto border-2 border-purple-400/50 rounded-full flex items-center justify-center backdrop-blur-sm">
                      <div className="w-24 h-24 border border-cyan-400/50 rounded-full flex items-center justify-center">
                        <motion.div
                          className="w-16 h-16 bg-gradient-to-br from-purple-400/30 to-cyan-400/30 rounded-full"
                          animate={{ scale: [1, 1.1, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        />
                      </div>
                    </div>
                  </motion.div>
                  
                  <HologramText
                    variant="secondary"
                    size="lg"
                    className="text-2xl mb-6"
                  >
                    检测到时空信号
                  </HologramText>
                  
                  <GlowButton
                    variant="primary"
                    size="lg"
                    onClick={startTransmission}
                    className="px-8 py-4"
                  >
                    开始接收通讯
                  </GlowButton>
                </motion.div>
              )}
              
              {isPlaying && (
                <motion.div
                  className="text-center w-full max-w-2xl"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.5 }}
                >
                  {/* 通讯头部 */}
                  <motion.div
                    className="mb-8"
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <div className="flex items-center justify-center space-x-4 mb-4">
                      <motion.div
                        className="w-2 h-2 rounded-full bg-green-400"
                        animate={{ opacity: [1, 0.3, 1] }}
                        transition={{ duration: 1, repeat: Infinity }}
                      />
                      <span className="text-green-400 font-mono text-sm">
                        通讯建立 - 信号强度: {Math.floor(Math.random() * 20) + 80}%
                      </span>
                      <motion.div
                        className="w-2 h-2 rounded-full bg-green-400"
                        animate={{ opacity: [1, 0.3, 1] }}
                        transition={{ duration: 1, repeat: Infinity, delay: 0.5 }}
                      />
                    </div>
                    
                    <div className="text-purple-400 font-mono text-lg">
                      来自过去的声音 #{currentAdvice + 1}
                    </div>
                  </motion.div>
                  
                  {/* 建议内容 */}
                  <motion.div
                    className="bg-gradient-to-br from-purple-900/20 to-blue-900/20 border border-purple-400/30 rounded-lg p-8 backdrop-blur-sm mb-8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                  >
                    <TypewriterText
                      text={WISDOM_ADVICES[currentAdvice]}
                      speed={50}
                      className="text-xl md:text-2xl text-cyan-200 leading-relaxed"
                    />
                    
                    <motion.div
                      className="mt-6 text-purple-300 font-mono text-sm"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 2 }}
                    >
                      - 来自过去的自己
                    </motion.div>
                  </motion.div>
                  
                  {/* 控制按钮 */}
                  <motion.div
                    className="flex justify-center space-x-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 3 }}
                  >
                    {currentAdvice > 0 && (
                      <GlowButton
                        variant="secondary"
                        size="md"
                        onClick={prevAdvice}
                        className="px-6 py-3"
                      >
                        ← 上一条
                      </GlowButton>
                    )}
                    
                    <GlowButton
                      variant="primary"
                      size="md"
                      onClick={nextAdvice}
                      className="px-6 py-3"
                    >
                      {currentAdvice < WISDOM_ADVICES.length - 1 ? '下一条 →' : '结束通讯'}
                    </GlowButton>
                  </motion.div>
                  
                  {/* 进度指示器 */}
                  <motion.div
                    className="flex justify-center space-x-2 mt-6"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 3.5 }}
                  >
                    {WISDOM_ADVICES.map((_, index) => (
                      <motion.div
                        key={index}
                        className={cn(
                          'w-2 h-2 rounded-full',
                          index === currentAdvice
                            ? 'bg-purple-400'
                            : index < currentAdvice
                            ? 'bg-green-400'
                            : 'bg-gray-600'
                        )}
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 3.5 + index * 0.1 }}
                      />
                    ))}
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
        
        {/* 导航按钮 */}
        <motion.div
          className="flex justify-between items-center mt-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2, duration: 0.8 }}
        >
          <GlowButton
            variant="secondary"
            size="md"
            onClick={prevPage}
            className="px-6 py-3"
          >
            ← 小行星带
          </GlowButton>
          
          <div className="text-center">
            <div className="text-purple-400 text-sm font-mono mb-2">
              时空通讯状态
            </div>
            <div className="flex space-x-2">
              {WISDOM_ADVICES.map((_, index) => (
                <motion.div
                  key={index}
                  className={cn(
                    'w-3 h-3 rounded-full border-2',
                    isPlaying && index === currentAdvice
                      ? 'bg-purple-400 border-purple-400'
                      : isPlaying && index < currentAdvice
                      ? 'bg-green-400 border-green-400'
                      : 'bg-purple-400/20 border-purple-400'
                  )}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 1.5 + index * 0.2 }}
                />
              ))}
            </div>
          </div>
          
          <GlowButton
            variant="primary"
            size="md"
            onClick={nextPage}
            className="px-6 py-3"
          >
            发现星云 →
          </GlowButton>
        </motion.div>
      </div>
      
      {/* 时空扭曲效果 */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: [0, 0.1, 0] }}
        transition={{
          duration: 5,
          repeat: Infinity,
          repeatDelay: 10
        }}
      >
        <div className="absolute inset-0 bg-gradient-radial from-purple-500/10 via-transparent to-transparent" />
      </motion.div>
    </div>
  );
}