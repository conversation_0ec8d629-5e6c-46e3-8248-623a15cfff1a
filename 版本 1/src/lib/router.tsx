import React from 'react';
import { CoverPage } from '../pages/CoverPage';
import { CrewPage } from '../pages/CrewPage';
import { JournalPage } from '../pages/JournalPage';
import { ChallengePage } from '../pages/ChallengePage';
import { WisdomPage } from '../pages/WisdomPage';
import { DiscoveryPage } from '../pages/DiscoveryPage';
import { AwardPreludePage } from '../pages/AwardPreludePage';
import { AwardCeremonyPage } from '../pages/AwardCeremonyPage';
import { CompletionPage } from '../pages/CompletionPage';

export interface RouteConfig {
  id: string;
  path: string;
  title: string;
  component: React.ComponentType;
  description: string;
  icon?: string;
}

export const ROUTES: RouteConfig[] = [
  {
    id: 'cover',
    path: '/',
    title: '封面',
    component: CoverPage,
    description: '任务报告：星光守护者计划',
    icon: '🚀'
  },
  {
    id: 'crew',
    path: '/crew',
    title: '舰桥船员',
    component: CrewPage,
    description: '守护者号核心船员介绍',
    icon: '👥'
  },
  {
    id: 'journal',
    path: '/journal',
    title: '航行日志',
    component: JournalPage,
    description: '闪亮的星尘记忆',
    icon: '📖'
  },
  {
    id: 'challenge',
    path: '/challenge',
    title: '挑战',
    component: ChallengePage,
    description: '穿越小行星带',
    icon: '⚠️'
  },
  {
    id: 'wisdom',
    path: '/wisdom',
    title: '智慧',
    component: WisdomPage,
    description: '来自过去的通讯',
    icon: '💡'
  },
  {
    id: 'discovery',
    path: '/discovery',
    title: '发现',
    component: DiscoveryPage,
    description: '发现效率星云',
    icon: '🔍'
  },
  {
    id: 'award-prelude',
    path: '/award-prelude',
    title: '颁奖铺垫',
    component: AwardPreludePage,
    description: '致敬我们的伙伴',
    icon: '🎭'
  },
  {
    id: 'award-ceremony',
    path: '/award-ceremony',
    title: '颁奖典礼',
    component: AwardCeremonyPage,
    description: '星光勋章授予仪式',
    icon: '🏆'
  },
  {
    id: 'completion',
    path: '/completion',
    title: '任务完成',
    component: CompletionPage,
    description: '成功返航',
    icon: '✅'
  }
];

// 根据索引获取路由
export function getRouteByIndex(index: number): RouteConfig | undefined {
  return ROUTES[index];
}

// 根据路径获取路由
export function getRouteByPath(path: string): RouteConfig | undefined {
  return ROUTES.find(route => route.path === path);
}

// 根据ID获取路由
export function getRouteById(id: string): RouteConfig | undefined {
  return ROUTES.find(route => route.id === id);
}

// 获取路由索引
export function getRouteIndex(routeId: string): number {
  return ROUTES.findIndex(route => route.id === routeId);
}

// 获取下一个路由
export function getNextRoute(currentIndex: number): RouteConfig | undefined {
  const nextIndex = currentIndex + 1;
  return nextIndex < ROUTES.length ? ROUTES[nextIndex] : undefined;
}

// 获取上一个路由
export function getPrevRoute(currentIndex: number): RouteConfig | undefined {
  const prevIndex = currentIndex - 1;
  return prevIndex >= 0 ? ROUTES[prevIndex] : undefined;
}

// 检查是否是第一页
export function isFirstPage(index: number): boolean {
  return index === 0;
}

// 检查是否是最后一页
export function isLastPage(index: number): boolean {
  return index === ROUTES.length - 1;
}

// 获取总页数
export function getTotalPages(): number {
  return ROUTES.length;
}

// 路由组件渲染器
export function RouteRenderer({ index }: { index: number }) {
  const route = getRouteByIndex(index);
  
  if (!route) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-red-400 text-xl font-mono">
          错误：页面不存在 (索引: {index})
        </div>
      </div>
    );
  }
  
  const Component = route.component;
  return <Component />;
}

// 导航辅助函数
export const navigationHelpers = {
  getRouteByIndex,
  getRouteByPath,
  getRouteById,
  getRouteIndex,
  getNextRoute,
  getPrevRoute,
  isFirstPage,
  isLastPage,
  getTotalPages
};

export default ROUTES;