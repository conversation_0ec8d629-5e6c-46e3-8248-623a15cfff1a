// 船员成员类型
export interface CrewMember {
  id: string;
  name: string;
  position: string;
  description: string;
  avatar: string;
  status?: 'active' | 'inactive' | 'mission';
  specialties?: string[];
  experience?: number;
  role?: string;
}

// 记忆/日志类型
export interface Memory {
  id: string;
  title: string;
  content: string;
  timestamp: string;
  type: 'log' | 'discovery' | 'achievement' | 'personal';
  author?: string;
  tags?: string[];
  importance?: number;
  starCluster?: string;
  position?: { x: number; y: number };
  color?: string;
}

// 挑战类型
export interface Challenge {
  id: string;
  title: string;
  description: string;
  difficulty: number;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  progress?: number;
  requirements?: string[];
  rewards?: string[];
  timeLimit?: number;
  category?: 'navigation' | 'technical' | 'social' | 'survival';
  solution?: string;
  position?: { x: number; y: number };
  color?: string;
}

// 任务指标类型
export interface MissionMetric {
  id: string;
  label: string;
  value: number;
  maxValue: number;
  unit: string;
  color: string;
  category: 'performance' | 'safety' | 'efficiency' | 'teamwork';
}

// 页面状态类型
export interface PageState {
  currentPage: number;
  totalPages: number;
  isTransitioning: boolean;
  direction: 'forward' | 'backward';
}

// 应用状态类型
export interface AppState {
  currentPage: number;
  crew: CrewMember[];
  memories: Memory[];
  challenges: Challenge[];
  missionMetrics: MissionMetric[];
  pageState: PageState;
  isLoading: boolean;
  error?: string;
}