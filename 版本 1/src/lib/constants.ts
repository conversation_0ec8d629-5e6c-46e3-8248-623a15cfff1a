import { CrewMember, Memory, Challenge } from './types';

// 船员常量
export const CREW_MEMBERS: CrewMember[] = [
  {
    id: '1',
    name: '赵明',
    position: '舰长',
    role: '主持',
    description: '经验丰富的舰长，负责整体指挥和协调工作。在关键时刻展现出卓越的领导能力。',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=futuristic%20starship%20captain%20portrait%20hologram%20style%20blue%20glow&image_size=square'
  },
  {
    id: '2',
    name: '马佳平',
    position: '大副',
    role: '后台导演',
    description: '可靠的大副，负责后台技术支持和流程管理。确保每个环节都能顺利进行。',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=futuristic%20first%20officer%20portrait%20hologram%20style%20green%20glow&image_size=square'
  },
  {
    id: '3',
    name: '蔡庆强',
    position: '总工程师',
    role: '技术',
    description: '技术专家，负责所有技术设备的维护和优化。在技术难题面前总能找到解决方案。',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=futuristic%20chief%20engineer%20portrait%20hologram%20style%20orange%20glow&image_size=square'
  },
  {
    id: '4',
    name: '陈柄宏',
    position: '领航员',
    role: 'VJ',
    description: '精准的领航员，负责视觉效果和航行路线规划。为团队提供清晰的方向指引。',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=futuristic%20navigator%20portrait%20hologram%20style%20purple%20glow&image_size=square'
  },
  {
    id: '5',
    name: '梁金隆',
    position: '通讯官',
    role: 'DJ',
    description: '专业的通讯官，负责音频系统和对外联络。确保信息传递准确无误。',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=futuristic%20communications%20officer%20portrait%20hologram%20style%20cyan%20glow&image_size=square'
  },
  {
    id: '6',
    name: '盛捷',
    position: '科学官',
    role: '计分',
    description: '细致的科学官，负责数据分析和计分系统。用科学的方法确保公平公正。',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=futuristic%20science%20officer%20portrait%20hologram%20style%20yellow%20glow&image_size=square'
  }
];

// 记忆常量
export const MEMORIES: Memory[] = [
  {
    id: '1',
    title: '数据处理挑战',
    content: '遭遇"有问必答"的挑战题，考验了我们的数据处理能力。',
    timestamp: '2024-01-15T10:30:00Z',
    type: 'achievement',
    starCluster: 'alpha',
    position: { x: 30, y: 40 },
    color: '#4FC3F7'
  },
  {
    id: '2',
    title: '友好接触',
    content: '与福建医科大学代表队初次接触，从他们认真的提问中感受到了赛事的价值。',
    timestamp: '2024-01-15T11:15:00Z',
    type: 'discovery',
    starCluster: 'beta',
    position: { x: 60, y: 25 },
    color: '#81C784'
  },
  {
    id: '3',
    title: '技术突破',
    content: '一个技术插曲：用最简单的方式，实现了完美的PPT无痕播放。',
    timestamp: '2024-01-15T14:20:00Z',
    type: 'achievement',
    starCluster: 'gamma',
    position: { x: 75, y: 65 },
    color: '#FFB74D'
  }
];

// 挑战常量
export const CHALLENGES: Challenge[] = [
  {
    id: '1',
    title: '计分表记录难题',
    description: '在比赛过程中遇到计分表记录的技术问题',
    difficulty: 3,
    status: 'completed',
    solution: '通过优化数据结构和实时同步机制，成功解决了计分表记录问题，确保了比赛的公平性！',
    position: { x: 20, y: 30 },
    color: '#F44336'
  },
  {
    id: '2',
    title: '寻找合适的氛围音乐',
    description: '为比赛寻找合适的背景音乐来营造氛围',
    difficulty: 2,
    status: 'completed',
    solution: '经过精心挑选，找到了完美契合比赛主题的音乐，为整个活动增添了专业感！',
    position: { x: 50, y: 45 },
    color: '#E91E63'
  },
  {
    id: '3',
    title: '计时器接口接触不良',
    description: '硬件计时器出现接口接触问题',
    difficulty: 4,
    status: 'completed',
    solution: '果断切换回纯软件方案，保障了终极PK环节的稳定性！',
    position: { x: 80, y: 60 },
    color: '#9C27B0'
  }
];

// 智慧建议常量
export const WISDOM_ADVICES = [
  '不要紧张，这只是一次很小的比赛。',
  '做好份内事，学会拒绝。',
  '别给自己太大压力，人生有很多容错率。'
];

// 赞美信息常量
export const PRAISE_MESSAGES = [
  { from: '匿名船员A', message: '为CTO送上小红花' },
  { from: '匿名船员B', message: '彩排时向云一直告诉我DJ的一些技巧' },
  { from: '匿名船员C', message: '为大梁送上小红花' },
  { from: '匿名船员D', message: '感谢技术团队的辛勤付出' },
  { from: '匿名船员E', message: '每个人都是团队不可或缺的一部分' },
  { from: '匿名船员F', message: '这次活动组织得非常成功' }
];

// 应用常量
export const APP_CONFIG = {
  TOTAL_PAGES: 9,
  ANIMATION_DURATION: {
    SHORT: 0.3,
    MEDIUM: 0.6,
    LONG: 1.0
  },
  COLORS: {
    PRIMARY: '#3b82f6',
    SECONDARY: '#8b5cf6',
    ACCENT: '#f59e0b',
    SUCCESS: '#10b981',
    WARNING: '#f59e0b',
    ERROR: '#ef4444'
  }
};