import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { CrewMember, Memory, Challenge } from './types';
import { crewMembers, memories, challenges } from './data';

// 应用状态接口
interface AppState {
  currentPage: number;
  isLoading: boolean;
  soundEnabled: boolean;
  crewMembers: CrewMember[];
  memories: Memory[];
  challenges: Challenge[];
  selectedCrewMember: CrewMember | null;
  selectedMemory: Memory | null;
  selectedChallenge: Challenge | null;
  showCard: boolean;
  animationPhase: string;
}

// 动作类型
type AppAction =
  | { type: 'SET_PAGE'; payload: number }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'TOGGLE_SOUND' }
  | { type: 'SELECT_CREW_MEMBER'; payload: CrewMember | null }
  | { type: 'SELECT_MEMORY'; payload: Memory | null }
  | { type: 'SELECT_CHALLENGE'; payload: Challenge | null }
  | { type: 'SHOW_CARD'; payload: boolean }
  | { type: 'SET_ANIMATION_PHASE'; payload: string };

// 初始状态
const initialState: AppState = {
  currentPage: 0,
  isLoading: true,
  soundEnabled: true,
  crewMembers,
  memories,
  challenges,
  selectedCrewMember: null,
  selectedMemory: null,
  selectedChallenge: null,
  showCard: false,
  animationPhase: 'initial'
};

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_PAGE':
      return { ...state, currentPage: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'TOGGLE_SOUND':
      return { ...state, soundEnabled: !state.soundEnabled };
    case 'SELECT_CREW_MEMBER':
      return { ...state, selectedCrewMember: action.payload };
    case 'SELECT_MEMORY':
      return { ...state, selectedMemory: action.payload };
    case 'SELECT_CHALLENGE':
      return { ...state, selectedChallenge: action.payload };
    case 'SHOW_CARD':
      return { ...state, showCard: action.payload };
    case 'SET_ANIMATION_PHASE':
      return { ...state, animationPhase: action.payload };
    default:
      return state;
  }
}

// Context
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

// Provider
export function AppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

// Hook
export function useAppContext() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}

// 辅助函数
export function useNavigation() {
  const { state, dispatch } = useAppContext();
  
  const nextPage = () => {
    if (state.currentPage < 8) {
      dispatch({ type: 'SET_PAGE', payload: state.currentPage + 1 });
    }
  };
  
  const prevPage = () => {
    if (state.currentPage > 0) {
      dispatch({ type: 'SET_PAGE', payload: state.currentPage - 1 });
    }
  };
  
  const goToPage = (page: number) => {
    if (page >= 0 && page <= 8) {
      dispatch({ type: 'SET_PAGE', payload: page });
    }
  };
  
  return {
    currentPage: state.currentPage,
    nextPage,
    prevPage,
    goToPage,
    isFirstPage: state.currentPage === 0,
    isLastPage: state.currentPage === 8
  };
}