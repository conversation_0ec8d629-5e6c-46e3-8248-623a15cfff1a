import { CrewMember, Memory, Challenge, WisdomAdvice, Praise } from '../types';

// 船员数据
export const crewData: CrewMember[] = [
  {
    id: '1',
    name: '赵明',
    position: '舰长',
    role: '主持',
    description: '负责整体协调和现场主持工作，确保活动流程顺畅进行'
  },
  {
    id: '2', 
    name: '马佳平',
    position: '大副',
    role: '后台导演',
    description: '负责后台技术支持和流程控制，保障技术环节稳定运行'
  },
  {
    id: '3',
    name: '蔡庆强', 
    position: '总工程师',
    role: '技术',
    description: '负责技术实现和问题解决，处理各种技术难题和突发状况'
  },
  {
    id: '4',
    name: '陈柄宏',
    position: '领航员', 
    role: 'VJ',
    description: '负责视觉效果和画面控制，营造完美的视觉体验'
  },
  {
    id: '5',
    name: '梁金隆',
    position: '通讯官',
    role: 'DJ', 
    description: '负责音响和氛围营造，为活动提供完美的听觉享受'
  },
  {
    id: '6',
    name: '盛捷',
    position: '科学官',
    role: '计分',
    description: '负责比赛计分和数据记录，确保比赛结果公正准确'
  }
];

// 记忆数据
export const memories: Memory[] = [
  {
    id: 'memory1',
    title: '数据处理挑战',
    content: '遭遇"有问必答"的挑战题，考验了我们的数据处理能力。',
    position: { x: 30, y: 40 }
  },
  {
    id: 'memory2', 
    title: '团队交流',
    content: '与福建医科大学代表队初次接触，从他们认真的提问中感受到了赛事的价值。',
    position: { x: 60, y: 25 }
  },
  {
    id: 'memory3',
    title: '技术突破', 
    content: '一个技术插曲：用最简单的方式，实现了完美的PPT无痕播放。',
    position: { x: 75, y: 60 }
  }
];

// 挑战数据
export const challenges: Challenge[] = [
  {
    id: 'challenge1',
    name: '计分表记录难题',
    solution: '建立了标准化的计分流程，确保记录准确性和实时性。',
    position: { x: 20, y: 30 }
  },
  {
    id: 'challenge2',
    name: '寻找合适的氛围音乐', 
    solution: '精心挑选了符合比赛氛围的背景音乐，提升了现场体验感。',
    position: { x: 20, y: 50 }
  },
  {
    id: 'challenge3',
    name: '计时器接口接触不良',
    solution: '果断切换回纯软件方案，保障了终极PK环节的稳定性！',
    position: { x: 20, y: 70 }
  }
];

// 智慧建议数据
export const wisdomAdvices: WisdomAdvice[] = [
  {
    id: 'advice1',
    content: '不要紧张，这只是一次很小的比赛。',
    delay: 1000
  },
  {
    id: 'advice2',
    content: '做好份内事，学会拒绝。',
    delay: 3000
  },
  {
    id: 'advice3',
    content: '别给自己太大压力，人生有很多容错率。',
    delay: 5000
  }
];

// 赞美数据
export const praises: Praise[] = [
  {
    id: 'praise1',
    content: '为CTO送上小红花'
  },
  {
    id: 'praise2',
    content: '彩排时向云一直告诉我DJ的一些技巧'
  },
  {
    id: 'praise3',
    content: '为大梁送上小红花'
  },
  {
    id: 'praise4',
    content: '感谢技术团队的专业支持'
  },
  {
    id: 'praise5',
    content: '主持人的现场控制力很棒'
  },
  {
    id: 'praise6',
    content: '音响效果调试得很完美'
  }
];

// 页面路由配置
export const pageRoutes = [
  { path: '/', name: '封面页' },
  { path: '/crew', name: '舰桥船员' },
  { path: '/log', name: '航行日志' },
  { path: '/challenges', name: '挑战解决' },
  { path: '/wisdom', name: '智慧建议' },
  { path: '/discovery', name: '重要发现' },
  { path: '/awards-intro', name: '颁奖铺垫' },
  { path: '/awards-ceremony', name: '颁奖仪式' },
  { path: '/summary', name: '任务总结' }
];

// 动画配置常量
export const animationConfig = {
  pageTransition: {
    duration: 0.8,
    ease: 'easeInOut'
  },
  cardHover: {
    duration: 0.3,
    scale: 1.05
  },
  textReveal: {
    duration: 0.05,
    stagger: 0.02
  },
  lightBeam: {
    duration: 2,
    ease: 'easeOut'
  },
  radarSweep: {
    duration: 3,
    ease: 'linear'
  }
};

// 颜色主题
export const theme = {
  colors: {
    primary: '#ffd700', // 金色
    secondary: '#e8e8e8', // 银白色
    background: '#0a0a0a', // 深邃黑色
    accent: '#00bfff', // 蓝色
    danger: '#ff4444', // 红色
    success: '#00ff88' // 绿色
  },
  gradients: {
    space: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
    gold: 'linear-gradient(45deg, #ffd700 0%, #ffed4e 100%)',
    hologram: 'linear-gradient(45deg, #00bfff 0%, #0080ff 100%)'
  }
};