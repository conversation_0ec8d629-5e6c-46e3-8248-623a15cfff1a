import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { AppState, AppAction } from '../types';
import { pageRoutes } from '../utils/constants';

// 初始状态
const initialState: AppState = {
  currentPageIndex: 0,
  isAnimationPlaying: false,
  isSoundEnabled: true,
  userProgress: 0
};

// Reducer函数
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_PAGE':
      return {
        ...state,
        currentPageIndex: action.payload,
        userProgress: Math.max(state.userProgress, action.payload)
      };
    case 'SET_ANIMATION_PLAYING':
      return {
        ...state,
        isAnimationPlaying: action.payload
      };
    case 'TOGGLE_SOUND':
      return {
        ...state,
        isSoundEnabled: !state.isSoundEnabled
      };
    case 'UPDATE_PROGRESS':
      return {
        ...state,
        userProgress: Math.max(state.userProgress, action.payload)
      };
    default:
      return state;
  }
}

// Context类型定义
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  // 便捷方法
  goToPage: (index: number) => void;
  nextPage: () => void;
  prevPage: () => void;
  toggleSound: () => void;
  setAnimationPlaying: (playing: boolean) => void;
}

// 创建Context
const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider组件
interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // 便捷方法
  const goToPage = (index: number) => {
    if (index >= 0 && index < pageRoutes.length) {
      dispatch({ type: 'SET_PAGE', payload: index });
    }
  };

  const nextPage = () => {
    const nextIndex = state.currentPageIndex + 1;
    if (nextIndex < pageRoutes.length) {
      dispatch({ type: 'SET_PAGE', payload: nextIndex });
    }
  };

  const prevPage = () => {
    const prevIndex = state.currentPageIndex - 1;
    if (prevIndex >= 0) {
      dispatch({ type: 'SET_PAGE', payload: prevIndex });
    }
  };

  const toggleSound = () => {
    dispatch({ type: 'TOGGLE_SOUND' });
  };

  const setAnimationPlaying = (playing: boolean) => {
    dispatch({ type: 'SET_ANIMATION_PLAYING', payload: playing });
  };

  const contextValue: AppContextType = {
    state,
    dispatch,
    goToPage,
    nextPage,
    prevPage,
    toggleSound,
    setAnimationPlaying
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
}

// Hook for using the context
export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}

// 页面导航Hook
export function usePageNavigation() {
  const { state, goToPage, nextPage, prevPage } = useApp();
  
  return {
    currentPageIndex: state.currentPageIndex,
    totalPages: pageRoutes.length,
    canGoNext: state.currentPageIndex < pageRoutes.length - 1,
    canGoPrev: state.currentPageIndex > 0,
    goToPage,
    nextPage,
    prevPage,
    progress: (state.currentPageIndex + 1) / pageRoutes.length * 100
  };
}

// 动画控制Hook
export function useAnimationControl() {
  const { state, setAnimationPlaying } = useApp();
  
  return {
    isAnimationPlaying: state.isAnimationPlaying,
    setAnimationPlaying,
    startAnimation: () => setAnimationPlaying(true),
    stopAnimation: () => setAnimationPlaying(false)
  };
}