import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AppProvider, usePageNavigation, useAnimationControl } from './contexts/AppContext';
import { RouteRenderer } from './lib/router';
import { LoadingSpinner } from './components/UI/ProgressBar';
import { cn } from './lib/utils';

// 页面过渡动画配置
const pageTransition = {
  initial: { opacity: 0, scale: 0.95, y: 20 },
  animate: { opacity: 1, scale: 1, y: 0 },
  exit: { opacity: 0, scale: 1.05, y: -20 }
};

// 主应用组件
function AppContent() {
  const { currentPageIndex } = usePageNavigation();
  const { isAnimationPlaying } = useAnimationControl();
  const soundEnabled = true; // 临时设置，需要从正确的context获取
  const [isLoading, setIsLoading] = React.useState(true);
  const [showContent, setShowContent] = React.useState(false);
  
  // 初始化加载
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
      setTimeout(() => setShowContent(true), 300);
    }, 2000);
    
    return () => clearTimeout(timer);
  }, []);
  
  // 键盘导航
  React.useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (isAnimationPlaying) return;
      
      // 这里可以添加键盘导航逻辑
      // 例如：左右箭头键切换页面
    };
    
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isAnimationPlaying]);
  
  // 加载屏幕
  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex flex-col items-center justify-center">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          <motion.div
            className="text-3xl-mobile sm:text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 mb-4 sm:mb-8"
            animate={{ 
              backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
            }}
            transition={{ duration: 3, repeat: Infinity, ease: 'linear' }}
            style={{ backgroundSize: '200% 200%' }}
          >
            星光守护者
          </motion.div>
          
          <motion.div
            className="text-cyan-400 text-lg-mobile sm:text-xl font-mono mb-4 sm:mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            正在初始化星舰系统...
          </motion.div>
          
          <LoadingSpinner size="lg" />
          
          <motion.div
            className="mt-4 sm:mt-8 text-cyan-500 text-sm-mobile sm:text-sm font-mono px-4 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 0.8 }}
          >
            请稍候，正在建立超空间连接...
          </motion.div>
        </motion.div>
        
        {/* 加载背景效果 */}
        <div className="absolute inset-0 overflow-hidden">
          {Array.from({ length: 50 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-cyan-400 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                opacity: [0, 1, 0],
                scale: [0, 1, 0]
              }}
              transition={{
                duration: 2 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2
              }}
            />
          ))}
        </div>
      </div>
    );
  }
  
  return (
    <div className={cn(
      "min-h-screen bg-black overflow-hidden relative",
      isAnimationPlaying && "pointer-events-none"
    )}>
      {/* 全局音效控制 */}
      {soundEnabled && (
        <audio
          autoPlay
          loop
          className="hidden"
        >
          <source src="/sounds/ambient-space.mp3" type="audio/mpeg" />
        </audio>
      )}
      
      {/* 页面内容 */}
      <AnimatePresence mode="wait">
        {showContent && (
          <motion.div
            key={currentPageIndex}
            {...pageTransition}
            transition={{ duration: 0.6 }}
            className="min-h-screen"
          >
            <RouteRenderer index={currentPageIndex} />
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* 页面指示器 */}
      <motion.div
        className="fixed bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 z-50"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: showContent ? 1 : 0, y: showContent ? 0 : 20 }}
        transition={{ delay: 1, duration: 0.8 }}
      >
        <div className="flex space-x-1 sm:space-x-2 px-2 sm:px-4 py-1 sm:py-2 bg-black/50 backdrop-blur-sm border border-cyan-400/30 rounded-full">
          {Array.from({ length: 9 }).map((_, index) => (
            <motion.div
              key={index}
              className={cn(
                "w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full transition-all duration-300",
                index === currentPageIndex
                  ? "bg-cyan-400 shadow-lg shadow-cyan-400/50"
                  : "bg-gray-600 hover:bg-gray-500"
              )}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
            />
          ))}
        </div>
      </motion.div>
      
      {/* 调试信息（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <motion.div
          className="fixed top-2 sm:top-4 right-2 sm:right-4 z-50 bg-black/80 backdrop-blur-sm border border-cyan-400/30 rounded-lg p-2 sm:p-3 text-xs-mobile sm:text-xs font-mono text-cyan-400"
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.7 }}
          transition={{ delay: 2 }}
        >
          <div>页面: {currentPageIndex + 1}/9</div>
          <div>动画: {isAnimationPlaying ? '进行中' : '空闲'}</div>
          <div>音效: {soundEnabled ? '开启' : '关闭'}</div>
        </motion.div>
      )}
    </div>
  );
}

// 根组件
export default function App() {
  return (
    <AppProvider>
      <AppContent />
    </AppProvider>
  );
}
